#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新增的保存和绘图功能
"""

def test_import():
    """测试导入功能"""
    try:
        from qmt_optuna_backtest import save_optimization_results, plot_trading_analysis
        print('✅ 新功能导入成功')
        return True
    except Exception as e:
        print(f'❌ 导入错误: {e}')
        return False

def test_basic_functionality():
    """测试基本功能"""
    try:
        import pandas as pd
        import numpy as np
        
        # 创建测试数据
        test_results = {
            'best_params': {'dt': 2000, 'tp_spread': 0.004},
            'best_value': 0.15,
            'train_results': {'total_return': 0.12, 'sharpe_ratio': 1.5},
            'test_results': {'total_return': 0.10, 'sharpe_ratio': 1.2},
            'generalization_ratio': 0.83
        }
        
        print('✅ 测试数据创建成功')
        return True
    except Exception as e:
        print(f'❌ 测试数据创建失败: {e}')
        return False

if __name__ == "__main__":
    print("🧪 测试新增功能...")
    
    # 测试导入
    if not test_import():
        exit(1)
    
    # 测试基本功能
    if not test_basic_functionality():
        exit(1)
    
    print("\n📊 新增功能说明:")
    print("1. 📁 自动保存优化结果到本地")
    print("   - JSON格式：完整的优化结果")
    print("   - Pickle格式：包含所有对象的二进制文件")
    print("   - Python配置文件：可直接导入使用的配置")
    print("   - 文本摘要：人类可读的结果总结")
    
    print("\n2. 📈 生成交易分析图表")
    print("   - 价格走势和开仓点图：显示买卖点位置")
    print("   - 收益曲线图：显示策略收益变化")
    print("   - 详细分析图：收益、回撤、交易分布、累计交易")
    print("   - 交易统计图：买卖对比、盈亏比例、时间分布")
    
    print("\n3. 🔄 自动化流程")
    print("   - 运行 run_optuna_optimization.py 后自动保存")
    print("   - 所有文件保存在 optimization_results 目录")
    print("   - 文件名包含时间戳，避免覆盖")
    
    print("\n✅ 所有测试通过！可以运行 run_optuna_optimization.py")
