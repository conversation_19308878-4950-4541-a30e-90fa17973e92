import pytest
import random
from utils.AS_api.src.calibration import SpreadIntensityCurve, AkSolverFactory

class TestSpreadIntensityCurve:
    """
    测试 spread - intensity 曲线在生成数据上的表现
    """

    def test_estimate_ak(self):
        """
        创建并测试 spread intensity 曲线
        使用生成的数据
        """
        tick_size = 1  # 用作 spreadStep
        n_spreads = 10  # 测试的 spread 数量，从 0 到 (n_spread-1)*tick_size
        w = 10000  # 滑动窗口大小

        p0 = 1000  # 起始价格
        price_ref = p0
        rng = random.Random()

        # 使用反射获取 intensityEstimates 字段
        lambda_estimates_field = SpreadIntensityCurve.__dict__['_SpreadIntensityCurve__intensityEstimates']
        lambda_estimates_field.setAccessible(True)

        sf = AkSolverFactory(AkSolverFactory.SolverType.MULTI_CURVE)
        est = SpreadIntensityCurve(tick_size, n_spreads, 1, sf)

        for i in range(w):
            price_ref += rng.gauss(0, 1)  # 模拟中间价格
            price_fill = price_ref - tick_size  # 买价
            est.onTick(price_ref, price_fill, i, 0)  # 将数据推入估计器

        est.estimateAk(w - 1, 0)  # 运行估计
        lambda_estimates = lambda_estimates_field.get(est)

        for i in range(1, len(lambda_estimates)):
            # 断言 lambda 的正确大小
            assert lambda_estimates[i] < lambda_estimates[i - 1]
