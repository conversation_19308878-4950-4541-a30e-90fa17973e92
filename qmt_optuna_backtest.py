import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, field
import os
import sys
from datetime import datetime
import optuna
from optuna.samplers import TPESampler
from optuna.pruners import MedianPruner
import logging
import warnings
import glob
import random
from collections import deque
import json
import pickle
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
import seaborn as sns

# 配置Optuna日志
optuna.logging.set_verbosity(optuna.logging.WARNING)

# 添加AS API路径
sys.path.append('../utils/AS_api/src')

# === 新增：延时相关类定义 ===
class PendingOrder:
    """待处理委托"""
    def __init__(self, order_id: str, action: str, price: float, quantity: int, 
                 submit_time: int, execute_time: int, reason: str):
        self.order_id = order_id
        self.action = action
        self.price = price
        self.quantity = quantity
        self.submit_time = submit_time
        self.execute_time = execute_time
        self.reason = reason
        self.status = 'pending'  # pending, filled, cancelled

# === 完全按照qmt_mm.py实现AS模型 ===
AS_API_AVAILABLE = True  # 使用内置实现

class EmpiricalIntensityEstimator:
    """
    限价单信息的容器 - 完全来自qmt_mm.py
    """
    class LimitOrderTracker:
        def __init__(self, start_ts: int, order_price: float):
            self.start_ts = start_ts
            self.order_price = order_price

    class Fill:
        def is_order_filled(self, filled_price: float, order_price: float) -> bool:
            raise NotImplementedError()

    class SellFill(Fill):
        def is_order_filled(self, filled_price: float, order_price: float) -> bool:
            return filled_price > order_price

    class BuyFill(Fill):
        def is_order_filled(self, filled_price: float, order_price: float) -> bool:
            return filled_price < order_price

    def __init__(self, spread: float, spread_direction: float, dt: int):
        """
        @param spread 与中间价的距离，买入限价单使用负号，卖出限价单使用正号
        @param spread_direction -1 表示卖出限价单，1 表示买入限
        """
        self.spread = spread
        self.dt = dt
        self.initializing = True
        self.last_price = float('nan')
        self.last_limit_order_inserted = 0
        self.live_trackers: List[EmpiricalIntensityEstimator.LimitOrderTracker] = []
        self.live_trackers_start_time_sum = 0
        self.finished_trackers: List[Tuple[int, int]] = []
        self.finished_trackers_wait_time_sum = 0
        self.fill_comp = self.SellFill() if spread_direction > 0 else self.BuyFill()

    def on_tick(self, ref_price: float, fill_price: float, ts: int, window_start: int):
        if self.initializing:
            self.initializing = False
            self.last_limit_order_inserted = ts - self.dt

        while self.last_limit_order_inserted + self.dt < ts:
            self.last_limit_order_inserted += self.dt
            self.live_trackers.append(
                self.LimitOrderTracker(self.last_limit_order_inserted, self.last_price + self.spread)
            )
            self.live_trackers_start_time_sum += self.last_limit_order_inserted

        if self.last_limit_order_inserted + self.dt == ts:
            self.last_limit_order_inserted = ts
            self.live_trackers.append(self.LimitOrderTracker(ts, ref_price + self.spread))
            self.live_trackers_start_time_sum += ts

        self.last_price = ref_price

        for tracker in self.live_trackers[:]:
            if window_start > tracker.start_ts:
                self.live_trackers.remove(tracker)
                self.live_trackers_start_time_sum -= tracker.start_ts
                continue

            if self.fill_comp.is_order_filled(fill_price, tracker.order_price):
                self.live_trackers.remove(tracker)
                self.live_trackers_start_time_sum -= tracker.start_ts
                duration = ts - tracker.start_ts
                self.finished_trackers.append((tracker.start_ts, duration))
                self.finished_trackers_wait_time_sum += duration

    def estimate_intensity(self, ts: int, window_start: int) -> float:
        for tracker in self.finished_trackers[:]:
            if tracker[0] < window_start:
                self.finished_trackers.remove(tracker)
                self.finished_trackers_wait_time_sum -= tracker[1]

        if self.live_trackers and ts != self.live_trackers[-1].start_ts:
            for tracker in self.live_trackers[:]:
                if window_start > tracker.start_ts:
                    self.live_trackers.remove(tracker)
                    self.live_trackers_start_time_sum -= tracker.start_ts

        if not self.live_trackers:
            return 0.0

        numerator = self.dt * len(self.finished_trackers)
        denominator = (
            len(self.live_trackers) * ts
            - self.live_trackers_start_time_sum
            + self.finished_trackers_wait_time_sum
        )

        if denominator == 0:
            return 0.0
        return max(float(numerator) / denominator, 1e-6)

class AbstractAkSolver:
    def __init__(self, spread_specification: np.ndarray):
        self.spread_specification = np.abs(spread_specification)

    def solve_ak(self, intensities: np.ndarray) -> np.ndarray:
        raise NotImplementedError("子类必须实现此方法")

class AkMultiCurveSolver(AbstractAkSolver):
    def __init__(self, spread_specification: np.ndarray):
        """
        近似求解A k - 完全按照qmt_mm.py的实现
        """
        super().__init__(spread_specification)
        n_estimates = len(spread_specification) * (len(spread_specification) - 1) // 2
        self.k_estimates = np.zeros(n_estimates)
        self.a_estimates = np.zeros(n_estimates)

    def solve_ak(self, intensities: np.ndarray) -> np.ndarray:
        est_idx = 0
        with np.errstate(divide='ignore', invalid='ignore'):
            for i in range(len(intensities) - 1):
                for j in range(i + 1, len(intensities)):
                    self.k_estimates[est_idx] = (
                        np.log(intensities[j] / intensities[i]) /
                        (self.spread_specification[i] - self.spread_specification[j])
                    )
                    self.a_estimates[est_idx] = (
                        intensities[i] * np.exp(self.k_estimates[est_idx] * self.spread_specification[i])
                    )
                    est_idx += 1

        return np.array([np.mean(self.a_estimates), np.mean(self.k_estimates)])

class SpreadIntensityCurve:
    def __init__(self, spread_step: float, n_spreads: int, dt: int, solver_factory=None):
        """
        价差-强度曲线 - 完全来自qmt_mm.py
        solver_factory参数仅为兼容性保留，不使用
        """
        self.intensity_estimators: List[EmpiricalIntensityEstimator] = []
        spread_specification = np.zeros(n_spreads)
        self.intensity_estimates = np.zeros(n_spreads)

        for i in range(n_spreads):
            spread_specification[i] = i * spread_step
            self.intensity_estimators.append(
                EmpiricalIntensityEstimator(spread_specification[i], np.sign(spread_step), dt)
            )
        self.solver = AkMultiCurveSolver(spread_specification)

    def on_tick(self, ref_price: float, fill_price: float, ts: int, window_start: int):
        for estimator in self.intensity_estimators:
            estimator.on_tick(ref_price, fill_price, ts, window_start)

    def estimate_ak(self, ts: int, window_start: int) -> np.ndarray:
        for i, estimator in enumerate(self.intensity_estimators):
            self.intensity_estimates[i] = estimator.estimate_intensity(ts, window_start)
        return self.solver.solve_ak(self.intensity_estimates)

class SolverType:
    MULTI_CURVE = 2

class AkSolverFactory:
    def __init__(self, solver_type=None):
        self.solver_type = solver_type

    def get_solver(self, spread_specification):
        return AkMultiCurveSolver(spread_specification)


# 强度价差预测函数 - 来自qmt_mm.py
def get_intensity(target_spread: float, a: float, k: float) -> float:
    import math
    return a * math.exp(-k * target_spread)

def get_spread_from_intensity(target_intensity: float, a: float, k: float) -> float:
    import math
    if target_intensity <= 0 or a <= 0:
        return 0.003
    try:
        return -(math.log(target_intensity / a)) / k
    except:
        return 0.003

@dataclass
class OptimizationConfig:
    """Optuna优化配置 - 优化版本（支持延时）"""
    # 基础参数范围
    initial_cash: float = 1000000.0  # 固定初始资金
    tick_size: float = 0.001  # 固定最小价格变动
    commission_rate_range: Tuple[float, float] = (0.0001, 0.0001)  # 手续费率范围
    
    # === 新增：延时参数范围 ===
    enable_latency: bool = True  # 是否启用延时模拟
    network_latency_mean_range: Tuple[int, int] = (50, 150)  # 网络延时均值范围(ms)
    network_latency_std_range: Tuple[int, int] = (10, 50)    # 网络延时标准差范围(ms)
    system_latency_range: Tuple[int, int] = (10, 30)        # 系统处理延时范围(ms)
    exchange_latency_range: Tuple[int, int] = (2, 10)       # 交易所延时范围(ms)
    slippage_rate_range: Tuple[float, float] = (0.00005, 0.0002)  # 滑点率范围
    market_impact_factor_range: Tuple[float, float] = (0.000005, 0.00002)  # 市场冲击因子范围
    order_timeout_range: Tuple[int, int] = (20000, 60000)   # 订单超时时间范围(ms)
    max_pending_orders_range: Tuple[int, int] = (5, 20)     # 最大待处理订单数范围
    
    # 策略参数范围（基于优化后的qmt_mm.py）
    max_position_range: Tuple[int, int] = (5000, 20000)  # 最大持仓范围
    
    # 原策略参数范围
    dt_range: Tuple[int, int] = (1000, 5000)  # 时间间隔范围(毫秒)
    window_size_range: Tuple[int, int] = (120000, 600000)  # 评估窗口范围(毫秒)
    
    # 挂单更新频率参数
    order_update_interval_range: Tuple[int, int] = (5000, 60000)  # 挂单更新间隔范围(毫秒) 5秒-60秒
    
    # 网格交易参数范围
    grid_levels_range: Tuple[int, int] = (2, 8)  # 网格层数范围
    base_quantity_range: Tuple[int, int] = (200, 2000)  # 基础下单量范围
    max_spread_range: Tuple[float, float] = (0.001, 0.005)  # 最大价差范围 - 大幅减少
    min_spread_range: Tuple[float, float] = (0.001, 0.002)  # 最小价差范围 - 大幅减少
    
    # === 优化7：动态网格间距参数范围 ===
    base_grid_spread_range: Tuple[float, float] = (0.002, 0.010)  # 基础网格间距范围
    min_grid_spread_range: Tuple[float, float] = (0.001, 0.005)   # 最小网格间距范围
    max_grid_spread_range: Tuple[float, float] = (0.005, 0.020)   # 最大网格间距范围
    volatility_window_range: Tuple[int, int] = (10, 50)           # 波动率计算窗口范围
    
    # 原策略参数范围
    ema_span_range: Tuple[float, float] = (0.002, 0.01)  # EMA span范围
    ema_spread_range: Tuple[float, float] = (-0.008, -0.001)  # EMA spread范围
    intensity_nspread_range: Tuple[int, int] = (3, 8)  # 强度估算价差层数范围
    tp_spread_range: Tuple[float, float] = (0.001, 0.008)  # 止盈价差范围
    
    # === 新增：风险偏好参数范围 ===
    risk_probility_range: Tuple[float, float] = (0.01, 0.08)  # 风险偏好范围 1%-8%
    risk_probility_buy_range: Tuple[float, float] = (0.01, 0.08)  # 买入风险偏好范围
    risk_probility_sell_range: Tuple[float, float] = (0.01, 0.08)  # 卖出风险偏好范围
    
    # 止盈参数范围 - 分层止盈已注释
    # tp_level1_pct_range: Tuple[float, float] = (0.001, 0.003)  # 第一层止盈百分比
    # tp_level1_ratio_range: Tuple[float, float] = (0.1, 0.8)  # 第一层止盈比例
    # tp_level2_pct_range: Tuple[float, float] = (0.002, 0.008)  # 第二层止盈百分比
    # tp_level2_ratio_range: Tuple[float, float] = (0.4, 0.7)  # 第二层止盈比例
    # tp_level3_pct_range: Tuple[float, float] = (0.005, 0.015)  # 第三层止盈百分比
    
    # === 新增：网格参数范围（与qmt_mm.py保持一致）===
    enter_lot_range: Tuple[int, int] = (3000, 8000)  # 初始买入手数范围
    grid_spread_range: Tuple[float, float] = (0.002, 0.012)  # 网格间距范围
    
    # 优化设置
    n_trials: int = 100  # 优化试验次数
    n_jobs: int = 1  # 并行作业数
    timeout: Optional[int] = None  # 超时时间(秒)
    
    # 止损参数范围
    sl_ratio_range: Tuple[float, float] = (0.5, 2.0)  # 止损盈亏比范围
    
@dataclass
class BacktestConfig:
    """回测配置 - 优化版本（支持延时）"""
    # 基础参数
    initial_cash: float = 1000000.0  # 初始资金
    max_position: int = 100000  # 最大持仓
    tick_size: float = 0.001  # 最小价格变动
    commission_rate: float = 0.0002  # 手续费率
    
    # === 新增：延时参数 ===
    enable_latency: bool = True       # 是否启用延时模拟
    network_latency_mean: int = 100   # 网络延时均值(ms)
    network_latency_std: int = 30     # 网络延时标准差(ms)
    system_latency: int = 20          # 系统处理延时(ms)
    exchange_latency: int = 5         # 交易所延时(ms)
    slippage_rate: float = 0.0001     # 滑点率
    market_impact_factor: float = 0.00001  # 市场冲击因子
    order_timeout: int = 30000        # 订单超时时间(ms)
    max_pending_orders: int = 10      # 最大待处理订单数
    
    # 策略参数（基于优化后的qmt_mm.py）
    dt: int = 3000  # 时间间隔(毫秒) - 原策略使用3000ms
    window_size: int = 210000  # 评估窗口大小(毫秒) - 原策略210秒
    
    # 挂单更新频率
    order_update_interval: int = 15000  # 挂单更新间隔(毫秒) - 默认15秒
    
    # 网格交易参数
    grid_levels: int = 3  # 网格层数
    base_quantity: int = 1000  # 基础下单量
    max_spread: float = 0.008  # 最大价差
    min_spread: float = 0.003  # 最小价差
    
    # === 优化7：动态网格间距参数 ===
    base_grid_spread: float = 0.005  # 基础网格间距
    min_grid_spread: float = 0.002   # 最小网格间距
    max_grid_spread: float = 0.010   # 最大网格间距
    volatility_window: int = 20      # 波动率计算窗口
    
    # 原策略的参数
    ema_span: float = 0.005  # EMA span
    ema_spread: float = -0.004  # EMA spread
    intensity_nspread: int = 5  # 强度估算的价差层数
    tp_spread: float = 0.003  # 止盈价差
    
    # === 新增：风险偏好参数 ===
    risk_probility: float = 0.03  # 风险偏好（默认3%）
    risk_probility_buy: float = 0.03   # 买入风险偏好
    risk_probility_sell: float = 0.03  # 卖出风险偏好
    
    # === 新增：网格参数（与qmt_mm.py保持一致）===
    enter_lot: int = 5000  # 初始买入手数
    grid_spread: float = 0.006  # 网格间距
    grid_spreadnet_param: List[float] = field(default_factory=lambda: [1, 1.5, 2, 2.5])  # 网格间距倍数
    grid_spreadqty_param: List[float] = field(default_factory=lambda: [1, 1, 2, 2])      # 网格数量倍数（与qmt_mm.py对齐）

    # 止盈参数 - 与qmt_mm.py对齐：两层止盈
    take_profit_levels: List[Tuple[float, float]] = field(default_factory=lambda: [
        (0.004, 0.5),   # 第一层：0.4%止盈，平50%仓位
        (0.008, 1.0)    # 第二层：0.8%止盈，全平
    ])

    # 止损参数（与qmt_mm.py对齐）
    sl_ratio: float = 4.0  # 止损盈亏比，与qmt_mm.py保持一致

@dataclass
class Trade:
    """交易记录"""
    timestamp: int
    price: float
    quantity: int
    side: str  # 'buy' or 'sell'
    commission: float
    pnl: float = 0.0

@dataclass
class Position:
    """持仓信息"""
    quantity: int = 0
    avg_price: float = 0.0
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0

class QMTOptunaBacktest:
    """QMT策略Optuna优化回测系统"""
    
    def __init__(self, config: BacktestConfig):
        self.config = config
        self.reset()
        
        # 买卖两个方向的价差-强度曲线
        self.buy_curve = SpreadIntensityCurve(
            spread_step=-config.tick_size,
            n_spreads=config.grid_levels,
            dt=config.dt
        )

        self.sell_curve = SpreadIntensityCurve(
            spread_step=config.tick_size,
            n_spreads=config.grid_levels,
            dt=config.dt
        )
        
        # === 新增：延时相关属性 ===
        if self.config.enable_latency:
            self.pending_orders: List[PendingOrder] = []
            self.current_tick_data: Dict = {}
            self.price_history_detailed: List[float] = []

        # === 新增：与qmt_mm.py对齐的状态变量 ===
        self.reach_tp1: bool = False  # 是否已达到第一次止盈
        self.if_add_vol: bool = False  # 是否已加仓（与qmt_mm.py保持一致）
        
        # === 新增：风险偏好和订单状态管理 ===
        self.pending_sell_orders = {}  # 待成交的卖出订单 {order_type: timestamp}
        self.pending_buy_orders = {}   # 待成交的买入订单 {order_type: timestamp}  
        self.order_timeout = 5000      # 订单超时时间 (ms)
        
        # AS模型相关
        self.current_time = 0
        self.last_mid_price = 0.0

        # === 按照qmt_mm.py的方式初始化AS模型 ===
        # 参数设置（参考qmt_mm.py）
        self.price_step = 0.001  # ETF最小价格差
        self.intensity_nspread = 5  # 强度估计的价差层数
        self.intensity_window = 180  # 估算时间长度（秒）

        # 初始化买卖两个方向的SpreadIntensityCurve（参考qmt_mm.py第336-337行）
        self.buy_est = SpreadIntensityCurve(
            spread_step=-self.price_step,  # 买入方向使用负价差
            n_spreads=self.intensity_nspread,
            dt=config.dt  # 使用配置中的dt
        )

        self.sell_est = SpreadIntensityCurve(
            spread_step=self.price_step,   # 卖出方向使用正价差
            n_spreads=self.intensity_nspread,
            dt=config.dt
        )

        # AS模型初始化完成
        
        # 挂单更新相关状态
        self.last_order_update_time = 0  # 上次挂单更新时间
        self.current_buy_price = 0.0     # 当前买单价格
        self.current_sell_price = 0.0    # 当前卖单价格

        # AS模型价差缓存（参考qmt_mm.py）
        self.last_valid_time = 0         # 上次有效价差时间
        self.last_bid_spread = 0.003     # 上次买入价差
        self.last_ask_spread = 0.003     # 上次卖出价差
        
        # === ETF网格交易状态管理（模拟QMT实盘逻辑）===
        self.has_position = False  # 是否有持仓（对应MM.sell_enble）
        self.has_added_position = False  # 是否已加仓（对应MM.if_add_vol）
        self.cost_price = 0.0  # 成本价（用于计算止盈和加仓）
        
        # === 新增：网格状态管理（与qmt_mm.py保持一致）===
        self.enter_price = 0.0  # 初始买入价格
        self.grid_add_layer = 0  # 当前网格层数
        
        # === 优化7：动态网格间距相关状态 ===
        self.price_history: List[float] = []  # 价格历史（用于计算波动率）
        self.current_grid_spread = self.config.base_grid_spread  # 当前网格间距
        
        # === 优化8：交易日志 ===
        self.trade_logs: List[Dict[str, Any]] = []  # 交易日志列表
        
    def reset(self):
        """重置回测状态"""
        self.cash = self.config.initial_cash
        self.position = Position()
        self.trades: List[Trade] = []
        
        # === 新增：延时相关状态 ===
        self.pending_orders: List[PendingOrder] = []  # 待处理订单队列
        self.order_counter = 0  # 订单计数器
        self.current_tick_data = None  # 当前tick数据
        self.price_history_detailed = deque(maxlen=100)  # 详细价格历史
        self.cancelled_orders = []  # 取消的订单
        self.equity_curve: List[Tuple[int, float]] = []
        self.current_time = 0
        self.last_mid_price = 0.0
        
        # 挂单更新相关状态
        self.last_order_update_time = 0  # 上次挂单更新时间
        self.current_buy_price = 0.0     # 当前买单价格
        self.current_sell_price = 0.0    # 当前卖单价格
        
        # === ETF网格交易状态管理（模拟QMT实盘逻辑）===
        self.has_position = False  # 是否有持仓（对应MM.sell_enble）
        self.has_added_position = False  # 是否已加仓（对应MM.if_add_vol）
        self.cost_price = 0.0  # 成本价（用于计算止盈和加仓）

        # === 新增：网格状态管理（与qmt_mm.py保持一致）===
        self.enter_price = 0.0  # 初始买入价格
        self.grid_add_layer = 0  # 当前网格层数

        # === 新增：与qmt_mm.py对齐的止盈状态 ===
        self.reach_tp1 = False  # 是否已达到第一次止盈
        self.if_add_vol = False  # 是否已加仓（与qmt_mm.py保持一致）

        # === 新增：订单状态重置 ===
        self.pending_sell_orders = {}  # 待成交的卖出订单
        self.pending_buy_orders = {}   # 待成交的买入订单

        # === 优化7：动态网格间距相关状态 ===
        self.price_history: List[float] = []  # 价格历史（用于计算波动率）
        self.current_grid_spread = self.config.base_grid_spread  # 当前网格间距

        # === 优化8：交易日志 ===
        self.trade_logs: List[Dict[str, Any]] = []  # 交易日志列表
        
    def load_data(self, file_path: str) -> pd.DataFrame:
        """加载回测数据 - 适配合并后的数据格式"""
        try:
            # 检查文件是否为合并后的数据格式
            df = pd.read_csv(file_path, encoding='utf-8')
            
            # 检查是否为新的合并数据格式
            if 'event_type' in df.columns and 'timestamp' in df.columns:
                return self._load_merged_data(df)
            else:
                return self._load_legacy_data(df, file_path)
                
        except UnicodeDecodeError:
            # 如果UTF-8失败，尝试GBK编码（原始数据）
            df = pd.read_csv(file_path, encoding='gbk')
            return self._load_legacy_data(df, file_path)
        except Exception as e:
            print(f"数据加载失败: {e}")
            raise
    
    def _load_merged_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """加载合并后的数据格式"""
        # 基本验证
        required_cols = ['timestamp', 'bid_price', 'ask_price', 'event_type']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            raise ValueError(f"缺少必要列: {missing_cols}")
        
        # 处理缺失值和无效数据
        df_clean = df.copy()
        
        # 确保数值列为正确类型
        numeric_cols = ['timestamp', 'last_price', 'bid_price', 'ask_price', 'bid_vol', 'ask_vol', 
                       'last_volume', 'trade_qty', 'trade_price']
        for col in numeric_cols:
            if col in df_clean.columns:
                df_clean[col] = pd.to_numeric(df_clean[col], errors='coerce')
        
        # 过滤有效的盘口数据
        valid_quotes = (
            (df_clean['bid_price'] > 0) & 
            (df_clean['ask_price'] > 0) &
            (df_clean['bid_price'] < df_clean['ask_price'])
        )
        
        df_clean = df_clean[valid_quotes].copy()
        
        # 对于没有last_price的行，使用mid_price或trade_price
        df_clean['last_price'] = df_clean['last_price'].fillna(
            df_clean['trade_price'].fillna((df_clean['bid_price'] + df_clean['ask_price']) / 2)
        )
        
        # 确保所有行都有有效的价格信息
        df_clean = df_clean[df_clean['last_price'] > 0].copy()
        
        # 按时间戳排序
        df_clean = df_clean.sort_values('timestamp').reset_index(drop=True)
        
        return df_clean
    
    def _load_legacy_data(self, df: pd.DataFrame, file_path: str) -> pd.DataFrame:
        """加载原始数据格式（保持向后兼容）"""
        # 转换时间为时间戳(毫秒)
        df['datetime'] = pd.to_datetime(df['自然日'] + ' ' + df['时间'])
        df['timestamp'] = ((df['datetime'] - pd.Timestamp("1970-01-01")) // pd.Timedelta('1ms')).astype('int64')
        
        # 提取关键字段
        df['last_price'] = pd.to_numeric(df['成交价'], errors='coerce')
        df['bid_price'] = pd.to_numeric(df['申买价1'], errors='coerce')
        df['ask_price'] = pd.to_numeric(df['申卖价1'], errors='coerce')
        df['bid_vol'] = pd.to_numeric(df['申买量1'], errors='coerce')
        df['ask_vol'] = pd.to_numeric(df['申卖量1'], errors='coerce')
        df['volume'] = pd.to_numeric(df['成交量'], errors='coerce')
        
        # 只对关键列进行空值处理
        key_columns = ['timestamp', 'last_price', 'bid_price', 'ask_price', 'bid_vol', 'ask_vol', 'volume']
        df_clean = df[key_columns].dropna(subset=['bid_price', 'ask_price'])
        
        # 如果成交价为0或NaN，则使用中间价代替
        mid_price = (df_clean['bid_price'] + df_clean['ask_price']) / 2
        df_clean['last_price'] = df_clean['last_price'].fillna(mid_price)
        df_clean.loc[df_clean['last_price'] <= 0, 'last_price'] = mid_price
        
        # 过滤无效数据
        result_df = df_clean[
            (df_clean['bid_price'] > 0) & 
            (df_clean['ask_price'] > 0) &
            (df_clean['bid_price'] < df_clean['ask_price']) &
            (df_clean['last_price'] > 0)
        ].copy()
        
        return result_df
    
    def calculate_mid_price(self, bid_price: float, ask_price: float) -> float:
        """计算中间价"""
        return (bid_price + ask_price) / 2.0
    
    # === 新增：订单状态管理函数（与qmt_mm.py保持一致）===
    def check_and_clean_pending_orders(self, current_time: int):
        """检查并清理超时的待处理订单"""
        # 清理超时的卖出订单
        for order_type in list(self.pending_sell_orders.keys()):
            if current_time - self.pending_sell_orders[order_type] > self.order_timeout:
                print(f"清理超时的卖出订单: {order_type}")
                del self.pending_sell_orders[order_type]
        
        # 清理超时的买入订单
        for order_type in list(self.pending_buy_orders.keys()):
            if current_time - self.pending_buy_orders[order_type] > self.order_timeout:
                print(f"清理超时的买入订单: {order_type}")
                del self.pending_buy_orders[order_type]

    def is_order_pending(self, order_type: str, order_direction: str = 'sell') -> bool:
        """检查指定类型的订单是否仍在待处理状态"""
        if order_direction == 'sell':
            return order_type in self.pending_sell_orders
        else:
            return order_type in self.pending_buy_orders

    def record_pending_order(self, order_type: str, current_time: int, order_direction: str = 'sell'):
        """记录待处理的订单"""
        if order_direction == 'sell':
            self.pending_sell_orders[order_type] = current_time
            print(f"记录待处理卖出订单: {order_type}")
        else:
            self.pending_buy_orders[order_type] = current_time
            print(f"记录待处理买入订单: {order_type}")

    def clear_pending_order_by_reason(self, reason: str):
        """根据交易原因清理对应的待处理订单"""
        # 映射交易原因到订单类型
        reason_to_order_type = {
            'take_profit_1': 'take_profit_1',
            'take_profit_2_full': 'take_profit_2_full',
            'simple_profit': 'simple_profit',
            'grid_buy_2': 'grid_buy_2',
            'stop_loss': 'stop_loss'
        }

        order_type = reason_to_order_type.get(reason)
        if order_type:
            # 检查卖出订单
            if order_type in self.pending_sell_orders:
                del self.pending_sell_orders[order_type]
                print(f"清理已成交的卖出订单: {order_type}")
            # 检查买入订单
            elif order_type in self.pending_buy_orders:
                del self.pending_buy_orders[order_type]
                print(f"清理已成交的买入订单: {order_type}")
    
    def calculate_dynamic_grid_spread(self) -> float:
        """根据价格历史计算动态网格间距"""
        if len(self.price_history) < 5:
            return self.config.base_grid_spread
        
        try:
            # 计算最近的价格波动率
            prices = np.array(self.price_history[-self.config.volatility_window:])
            if len(prices) < 2:
                return self.config.base_grid_spread
                
            returns = np.diff(prices) / prices[:-1]
            volatility = np.std(returns) if len(returns) > 1 else 0
            
            # 根据波动率调整网格间距
            volatility_factor = max(0.5, min(2.0, float(volatility / 0.01)))  # 标准化到0.5-2.0倍
            dynamic_spread = self.config.base_grid_spread * volatility_factor
            
            # 限制在合理范围内
            return max(self.config.min_grid_spread, min(self.config.max_grid_spread, dynamic_spread))
        except:
            return self.config.base_grid_spread
    
    # === 优化8：交易日志记录 ===
    def log_trade_action(self, action: str, price: float, quantity: int, reason: str, 
                        position_before: int = 0, timestamp: int = 0) -> Dict[str, Any]:
        """记录交易动作"""
        log_entry = {
            'timestamp': timestamp,
            'datetime': pd.to_datetime(timestamp, unit='ms').strftime('%Y-%m-%d %H:%M:%S'),
            'action': action,
            'price': price,
            'quantity': quantity,
            'reason': reason,
            'position_before': position_before,
            'position_after': position_before + (quantity if action == 'buy' else -quantity)
        }
        
        self.trade_logs.append(log_entry)
        
        # 保持日志数量在合理范围内
        if len(self.trade_logs) > 1000:
            self.trade_logs = self.trade_logs[-500:]  # 保留最近500条
        
        return log_entry
    
    def update_as_model(self, mid_price: float, bid_price: float, ask_price: float, timestamp: int):
        """更新AS模型 - 完全按照qmt_mm.py第394-395行的方式"""
        try:
            # 计算时间窗口（参考qmt_mm.py第394-395行）
            window_start = timestamp - self.intensity_window * 1000  # 转换为毫秒

            # 完全按照qmt_mm.py第394-395行的顺序和参数
            # 注意：qmt_mm.py中是先sell_est，后buy_est

            self.sell_est.on_tick(mid_price, ask_price, timestamp, window_start)
            self.buy_est.on_tick(mid_price, bid_price, timestamp, window_start)

        except Exception:
            pass  # 静默处理AS模型更新失败
    
    
    def get_spread(self, target_intensity: float, a: float, k: float) -> float:
        """根据目标强度计算价差（与qmt_mm.py保持一致）"""
        try:
            import math
            if a <= 0 or k <= 0 or target_intensity <= 0:
                return 0.001  # 默认最小spread
            return -(math.log(target_intensity / a)) / k
        except:
            return 0.001  # 计算失败时返回默认值

    def get_optimal_spreads(self, timestamp: int) -> Tuple[float, float]:
        """获取最优买卖价差 - 完全按照qmt_mm.py第394-430行的逻辑"""
            # 计算时间窗口（qmt_mm.py第394-395行）
        window_start = timestamp - self.intensity_window * 1000

        # 获取A和k值（qmt_mm.py第397-398行）
        buy_ak = self.buy_est.estimate_ak(timestamp, window_start)
        sell_ak = self.sell_est.estimate_ak(timestamp, window_start)

        # 初始化价差（qmt_mm.py第402-403行）
        bid_spread = 0.000
        ask_spread = 0.000

        # 价差计算逻辑（qmt_mm.py第404-416行）
        if not np.any(np.isnan(buy_ak)):
            if buy_ak[0] * buy_ak[1] != 0:
                filtered_bid_intensity = self.buy_est.intensity_estimates[self.buy_est.intensity_estimates > 1e-6]
                bid_spread = self.get_spread(self.config.risk_probility, buy_ak[0], buy_ak[1])
                bid_spread = max(bid_spread, 0.0015)

            if sell_ak[0] * sell_ak[1] != 0:
                filtered_ask_intensity = self.sell_est.intensity_estimates[self.sell_est.intensity_estimates > 1e-6]
                if len(filtered_ask_intensity) and filtered_ask_intensity[-1] * sell_ak[0] > 0:
                    if len(filtered_ask_intensity) < 4:
                        ask_spread = self.get_spread(filtered_ask_intensity[-1], sell_ak[0], sell_ak[1]) + 0.001
                    else:
                        ask_spread = self.get_spread(filtered_ask_intensity[3], sell_ak[0], sell_ak[1])

        # 价差有效性检查（qmt_mm.py第417-430行）
        spread_lifetime = 9000
        high_vol = True  # 简化版本，假设高波动

        # 检查价差有效性
        if timestamp - getattr(self, 'last_valid_time', 0) > spread_lifetime and bid_spread <= 0:
            bid_spread = 0.004 if high_vol else 0.003
            ask_spread = 0.004 if high_vol else 0.003
        elif timestamp - getattr(self, 'last_valid_time', 0) <= spread_lifetime and bid_spread <= 0:
            # 使用上次的价差
            bid_spread = getattr(self, 'last_bid_spread', 0.003)
            ask_spread = getattr(self, 'last_ask_spread', 0.003)
        else:
            # 更新有效时间
            self.last_valid_time = timestamp
            # 保存当前价差
            self.last_bid_spread = bid_spread
            self.last_ask_spread = ask_spread

        return bid_spread, ask_spread


    
    def calculate_position_size(self, price: float, side: str) -> int:
        """计算下单量"""
        current_pos = abs(self.position.quantity)
        max_pos = self.config.max_position
        
        if current_pos >= max_pos:
            return 0
        
        # 使用base_quantity作为基础下单量
        remaining_capacity = max_pos - current_pos
        base_size = min(self.config.base_quantity, remaining_capacity)
        
        # 资金检查
        required_cash = price * base_size
        if required_cash > self.cash * 0.8:  # 保留20%资金
            return int(self.cash * 0.8 / price)
        
        return base_size
    
    # === 新增：延时相关方法 ===
    def generate_latency(self) -> int:
        """生成随机网络延时"""
        if not self.config.enable_latency:
            return 0
            
        latency = np.random.normal(self.config.network_latency_mean, self.config.network_latency_std)
        total_latency = max(10, int(latency)) + self.config.system_latency + self.config.exchange_latency
        return total_latency
    
    def calculate_slippage(self, action: str, price: float, quantity: int) -> float:
        """计算滑点"""
        if not self.config.enable_latency:
            return 0.0
            
        # 基础滑点
        base_slippage = self.config.slippage_rate * price
        
        # 市场冲击（基于交易量）
        market_impact = self.config.market_impact_factor * quantity * price
        
        # 价格变动影响（基于最近价格波动）
        price_volatility = 0.0
        if len(self.price_history_detailed) > 1:
            recent_prices = list(self.price_history_detailed)[-10:]
            price_volatility = float(np.std(recent_prices)) if len(recent_prices) > 1 else 0.0
        
        total_slippage = base_slippage + market_impact + price_volatility * 0.1
        
        # 买入时价格上涨，卖出时价格下跌
        if action == 'buy':
            return float(total_slippage)
        else:
            return float(-total_slippage)
    
    def submit_order(self, price: float, quantity: int, side: str, timestamp: int, reason: str = "trade") -> str:
        """提交订单（根据配置决定是否立即成交）"""
        if not self.config.enable_latency:
            # 兼容模式：立即执行
            result = self.execute_trade_immediate(price, quantity, side, timestamp, reason)
            return f"IMMEDIATE_{timestamp}_{side}" if result else f"FAILED_{timestamp}_{side}"
        
        # 延时模式：提交待处理订单
        order_id = f"ORDER_{self.order_counter}_{timestamp}"
        self.order_counter += 1
        
        # 计算执行时间（当前时间 + 延时）
        latency = self.generate_latency()
        execute_time = timestamp + latency
        
        # 创建待处理订单
        order = PendingOrder(order_id, side, price, quantity, timestamp, execute_time, reason)
        self.pending_orders.append(order)
        
        # 限制待处理订单数量
        if len(self.pending_orders) > self.config.max_pending_orders:
            # 取消最老的订单
            old_order = self.pending_orders.pop(0)
            old_order.status = 'cancelled'
            self.cancelled_orders.append(old_order)
        
        return order_id
    
    def process_pending_orders(self, timestamp: int):
        """处理到期的待处理订单"""
        if not self.config.enable_latency:
            return
            
        current_tick = self.current_tick_data
        if current_tick is None:
            return
        
        for order in self.pending_orders[:]:
            # 检查是否到期执行
            if timestamp >= order.execute_time:
                # 检查订单是否超时
                if timestamp - order.submit_time > self.config.order_timeout:
                    order.status = 'cancelled'
                    self.cancelled_orders.append(order)
                    self.pending_orders.remove(order)
                    continue
                
                # 尝试执行订单
                if self.execute_order_with_slippage(order, current_tick, timestamp):
                    self.pending_orders.remove(order)
    
    def execute_order_with_slippage(self, order: PendingOrder, current_tick: Dict, timestamp: int) -> bool:
        """考虑滑点执行订单"""
        # 获取当前市场价格
        bid_price = float(current_tick['bid_price'])
        ask_price = float(current_tick['ask_price'])
        mid_price = (bid_price + ask_price) / 2
        
        # 检查价格是否还有效（简单的成交条件检查）
        if order.action == 'buy':
            # 买单：当前价格低于等于委托价格时成交
            if mid_price <= order.price:
                execution_price = min(order.price, ask_price)  # 不能低于当前卖价
            else:
                return False  # 价格不满足，不成交
        else:
            # 卖单：当前价格高于等于委托价格时成交
            if mid_price >= order.price:
                execution_price = max(order.price, bid_price)  # 不能高于当前买价
            else:
                return False  # 价格不满足，不成交
        
        # 计算滑点
        slippage = self.calculate_slippage(order.action, execution_price, order.quantity)
        final_price = execution_price + slippage
        
        # 确保价格合理
        final_price = max(0.001, final_price)
        
        # 执行交易
        return self.execute_trade_immediate(final_price, order.quantity, order.action, timestamp, order.reason)
    
    def execute_trade(self, price: float, quantity: int, side: str, timestamp: int, 
                     reason: str = "trade") -> bool:
        """执行交易（入口方法，根据配置选择执行方式）"""
        if self.config.enable_latency:
            # 延时模式：提交订单
            order_id = self.submit_order(price, quantity, side, timestamp, reason)
            return order_id.startswith("ORDER_")  # 提交成功返回True
        else:
            # 立即执行模式
            return self.execute_trade_immediate(price, quantity, side, timestamp, reason)
    
    def execute_trade_immediate(self, price: float, quantity: int, side: str, timestamp: int, 
                     reason: str = "trade") -> bool:
        """执行交易 - 立即执行版本"""
        if quantity <= 0:
            return False
        
        # === 优化4：检查最大持仓限制 ===
        current_position = abs(self.position.quantity)
        if side == 'buy' and current_position + quantity > self.config.max_position:
            print(f"超过最大持仓限制，当前:{current_position}, 最大:{self.config.max_position}")
            return False
        
        commission = price * quantity * self.config.commission_rate
        total_cost = price * quantity + commission
        trade_pnl = 0.0  # 单笔交易的盈亏
        position_before = self.position.quantity
        
        # === 优化8：记录交易日志 ===
        self.log_trade_action(side, price, quantity, reason, position_before, timestamp)
        
        if side == 'buy':
            if total_cost > self.cash:
                return False
            self.cash -= total_cost
            
            # 更新持仓
            if self.position.quantity >= 0:
                # 增加多头持仓 - 开仓交易，PnL为0
                total_value = self.position.avg_price * self.position.quantity + price * quantity
                self.position.quantity += quantity
                self.position.avg_price = total_value / self.position.quantity if self.position.quantity > 0 else 0.0
                trade_pnl = 0.0  # 开仓交易PnL为0
            else:
                # 减少空头持仓 - 平仓交易，计算PnL
                if quantity >= abs(self.position.quantity):
                    # 完全平仓并反向开仓
                    close_quantity = abs(self.position.quantity)
                    remaining_quantity = quantity - close_quantity
                    
                    # 平仓PnL - 空头平仓：买入价格低于开仓价格为盈利
                    close_pnl = (self.position.avg_price - price) * close_quantity
                    self.position.realized_pnl += close_pnl
                    trade_pnl = close_pnl - commission  # 单笔交易PnL包含手续费
                    
                    # 新开仓
                    if remaining_quantity > 0:
                        self.position.quantity = remaining_quantity
                        self.position.avg_price = price
                    else:
                        self.position.quantity = 0
                        self.position.avg_price = 0.0
                else:
                    # 部分平仓
                    close_pnl = (self.position.avg_price - price) * quantity
                    self.position.realized_pnl += close_pnl
                    trade_pnl = close_pnl - commission  # 单笔交易PnL包含手续费
                    self.position.quantity += quantity  # 空头持仓减少
        
        else:  # sell
            self.cash += price * quantity - commission
            
            # 更新持仓
            if self.position.quantity <= 0:
                # 增加空头持仓 - 开仓交易，PnL为0
                total_value = abs(self.position.avg_price * self.position.quantity) + price * quantity
                self.position.quantity -= quantity
                self.position.avg_price = total_value / abs(self.position.quantity) if self.position.quantity != 0 else 0.0
                trade_pnl = 0.0  # 开仓交易PnL为0
            else:
                # 减少多头持仓 - 平仓交易，计算PnL
                if quantity >= self.position.quantity:
                    # 完全平仓并反向开仓
                    close_quantity = self.position.quantity
                    remaining_quantity = quantity - close_quantity
                    
                    # 平仓PnL - 多头平仓：卖出价格高于开仓价格为盈利
                    close_pnl = (price - self.position.avg_price) * close_quantity
                    self.position.realized_pnl += close_pnl
                    trade_pnl = close_pnl - commission  # 单笔交易PnL包含手续费
                    
                    # 新开仓
                    if remaining_quantity > 0:
                        self.position.quantity = -remaining_quantity
                        self.position.avg_price = price
                    else:
                        self.position.quantity = 0
                        self.position.avg_price = 0.0
                else:
                    # 部分平仓
                    close_pnl = (price - self.position.avg_price) * quantity
                    self.position.realized_pnl += close_pnl
                    trade_pnl = close_pnl - commission  # 单笔交易PnL包含手续费
                    self.position.quantity -= quantity
        
        # 记录交易 - 现在包含正确的PnL
        trade = Trade(
            timestamp=timestamp,
            price=price,
            quantity=quantity,
            side=side,
            commission=commission,
            pnl=trade_pnl  # 正确的单笔交易PnL
        )
        self.trades.append(trade)
        
        return True
    
    def check_take_profit(self, current_price: float, timestamp: int):
        """检查止盈 - 使用tp_spread参数"""
        if self.position.quantity == 0:
            return
        
        # 使用tp_spread作为止盈触发条件
        if self.position.quantity > 0:  # 多头持仓
            profit_pct = (current_price - self.position.avg_price) / self.position.avg_price
            if profit_pct >= self.config.tp_spread:
                # 执行止盈
                tp_quantity = int(self.position.quantity * 0.5)  # 平50%仓位
                if tp_quantity > 0:
                    self.execute_trade(current_price, tp_quantity, 'sell', timestamp)
        
        else:  # 空头持仓
            profit_pct = (self.position.avg_price - current_price) / self.position.avg_price
            if profit_pct >= self.config.tp_spread:
                # 执行止盈
                tp_quantity = int(abs(self.position.quantity) * 0.5)  # 平50%仓位
                if tp_quantity > 0:
                    self.execute_trade(current_price, tp_quantity, 'buy', timestamp)
    
    def update_unrealized_pnl(self, current_price: float):
        """更新未实现盈亏"""
        if self.position.quantity != 0:
            self.position.unrealized_pnl = (current_price - self.position.avg_price) * self.position.quantity
    
    def get_total_equity(self, current_price: float) -> float:
        """获取总权益"""
        return self.cash + self.position.realized_pnl + self.position.unrealized_pnl
    
    def run_backtest(self, data: pd.DataFrame) -> Dict[str, Any]:
        """运行回测 - 按照qmt_mm.py的逻辑"""
        self.reset()

        for idx, row in data.iterrows():
            timestamp = int(row['timestamp'])
            bid_price = float(row['bid_price'])
            ask_price = float(row['ask_price'])
            last_price = float(row['last_price'])

            try:
                # 计算中间价（参考qmt_mm.py第391行）
                mid_price = (bid_price + ask_price) / 2

                # 更新AS模型（参考qmt_mm.py第394-395行）
                self.update_as_model(mid_price, bid_price, ask_price, timestamp)

                # 检查是否需要更新挂单价格
                should_update_orders = (
                    self.last_order_update_time == 0 or  # 首次更新
                    timestamp - self.last_order_update_time >= self.config.order_update_interval  # 到达更新间隔
                )

                if should_update_orders:
                    # 获取最优价差
                    buy_spread, sell_spread = self.get_optimal_spreads(timestamp)

                    # 更新挂单价格（按照您修改的逻辑）
                    self.current_buy_price = bid_price - buy_spread  # 使用动态买入价差
                    self.current_sell_price = ask_price + sell_spread  # 使用动态卖出价差
                    self.last_order_update_time = timestamp

                    # 打印买入挂单的spread和价格

                # 检查交易信号和执行交易
                self.check_trading_signals(timestamp, bid_price, ask_price, last_price)

                # 更新权益曲线
                current_equity = self.cash + self.position.realized_pnl + self.position.unrealized_pnl
                self.equity_curve.append((timestamp, current_equity))

            except Exception:
                continue

        return self.calculate_results()

    def check_trading_signals(self, timestamp: int, bid_price: float, ask_price: float, last_price: float):
        """检查交易信号并执行交易"""
        try:
            # 检查买入信号
            if self.should_buy(timestamp, bid_price, ask_price, last_price):
                quantity = self.calculate_position_size(self.current_buy_price, 'buy')
                if quantity > 0:
                    self.execute_trade('buy', self.current_buy_price, quantity, timestamp)

            # 检查卖出信号
            if self.should_sell(timestamp, bid_price, ask_price, last_price):
                quantity = self.calculate_position_size(self.current_sell_price, 'sell')
                if quantity > 0:
                    self.execute_trade('sell', self.current_sell_price, quantity, timestamp)

        except Exception:
            pass

    def should_buy(self, timestamp: int, bid_price: float, ask_price: float, last_price: float) -> bool:
        """判断是否应该买入"""
        # 简化的买入逻辑
        if self.position.quantity >= self.config.max_position:
            return False

        # 检查价格是否触及买入价格
        return last_price <= self.current_buy_price

    def should_sell(self, timestamp: int, bid_price: float, ask_price: float, last_price: float) -> bool:
        """判断是否应该卖出"""
        # 简化的卖出逻辑
        if self.position.quantity <= -self.config.max_position:
            return False

        # 检查价格是否触及卖出价格
        return last_price >= self.current_sell_price

    def execute_trade(self, side: str, price: float, quantity: int, timestamp: int):
        """执行交易"""
        try:
            commission = price * quantity * self.config.commission_rate

            if side == 'buy':
                # 买入
                cost = price * quantity + commission
                if cost <= self.cash:
                    self.cash -= cost
                    self.position.quantity += quantity
                    self.position.avg_price = ((self.position.avg_price * (self.position.quantity - quantity)) +
                                             (price * quantity)) / self.position.quantity

                    # 记录交易
                    trade = Trade(timestamp=timestamp, price=price, quantity=quantity,
                                side=side, commission=commission, pnl=0)
                    self.trades.append(trade)

            elif side == 'sell':
                # 卖出
                revenue = price * quantity - commission
                self.cash += revenue

                # 计算盈亏
                pnl = (price - self.position.avg_price) * quantity - commission
                self.position.realized_pnl += pnl
                self.position.quantity -= quantity

                # 记录交易
                trade = Trade(timestamp=timestamp, price=price, quantity=quantity,
                            side=side, commission=commission, pnl=pnl)
                self.trades.append(trade)

        except Exception:
            pass

    def calculate_results(self) -> Dict[str, Any]:
        """计算回测结果 - 优化版本"""
        if len(self.equity_curve) == 0:
            return {
                'total_return': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown': 0.0,
                'total_trades': 0,
                'win_rate': 0.0,
                'profit_factor': 0.0,
                'final_equity': self.config.initial_cash,
                'avg_profit_per_trade': 0.0,
                'avg_loss_per_trade': 0.0,
                'profit_loss_ratio': 0.0,
                'trade_log_summary': self._get_trade_log_summary()
            }
        
        # 计算收益率
        initial_equity = self.config.initial_cash
        final_equity = self.equity_curve[-1][1]
        total_return = (final_equity - initial_equity) / initial_equity
        
        # 计算最大回撤
        equity_values = [eq[1] for eq in self.equity_curve]
        peak = equity_values[0]
        max_drawdown = 0.0
        
        for equity in equity_values:
            if equity > peak:
                peak = equity
            drawdown = (peak - equity) / peak
            max_drawdown = max(max_drawdown, drawdown)
        
        # 计算夏普比率
        if len(equity_values) > 1:
            returns = np.diff(equity_values) / equity_values[:-1]
            if np.std(returns) > 0:
                sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252 * 24 * 60)  # 年化
            else:
                sharpe_ratio = 0.0
        else:
            sharpe_ratio = 0.0
        
        # === 优化8：增强的交易统计 ===
        total_trades = len(self.trades)



        if total_trades > 0:
            winning_trades = sum(1 for trade in self.trades if trade.pnl > 0)
            losing_trades = sum(1 for trade in self.trades if trade.pnl < 0)
            zero_trades = total_trades - winning_trades - losing_trades

            win_rate = winning_trades / total_trades

            gross_profit = sum(trade.pnl for trade in self.trades if trade.pnl > 0)
            gross_loss = abs(sum(trade.pnl for trade in self.trades if trade.pnl < 0))
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')

            # 平均盈利和亏损
            avg_profit_per_trade = gross_profit / winning_trades if winning_trades > 0 else 0.0
            avg_loss_per_trade = gross_loss / losing_trades if losing_trades > 0 else 0.0
            profit_loss_ratio = avg_profit_per_trade / avg_loss_per_trade if avg_loss_per_trade > 0 else float('inf')

        else:
            win_rate = 0.0
            profit_factor = 0.0
            avg_profit_per_trade = 0.0
            avg_loss_per_trade = 0.0
            profit_loss_ratio = 0.0
            winning_trades = 0
            losing_trades = 0
            zero_trades = 0
        
        return {
            'total_return': total_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'zero_trades': zero_trades,
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'avg_profit_per_trade': avg_profit_per_trade,
            'avg_loss_per_trade': avg_loss_per_trade,
            'profit_loss_ratio': profit_loss_ratio,
            'final_equity': final_equity,
            'realized_pnl': self.position.realized_pnl,
            'unrealized_pnl': self.position.unrealized_pnl,
            'trade_log_summary': self._get_trade_log_summary()
        }
    
    def _get_trade_log_summary(self) -> Dict[str, Any]:
        """获取交易日志统计摘要"""
        if not self.trade_logs:
            return {'total_actions': 0, 'buy_actions': 0, 'sell_actions': 0, 'action_types': {}}
        
        buy_actions = len([log for log in self.trade_logs if log['action'] == 'buy'])
        sell_actions = len([log for log in self.trade_logs if log['action'] == 'sell'])
        
        # 按交易原因分类统计
        action_types = {}
        for log in self.trade_logs:
            reason = log['reason']
            if reason not in action_types:
                action_types[reason] = 0
            action_types[reason] += 1
        
        return {
            'total_actions': len(self.trade_logs),
            'buy_actions': buy_actions,
            'sell_actions': sell_actions,
            'action_types': action_types
        }



def plot_daily_analysis(backtest: 'QMTOptunaBacktest', data: pd.DataFrame, save_dir: str = "optimization_results", filename_prefix: str = "daily") -> Dict[str, str]:
    """按日期分别绘制交易分析图表"""
    print(f"📊 开始按日期绘制图表，数据量: {len(data)} 个点")

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False

    # 创建保存目录
    os.makedirs(save_dir, exist_ok=True)
    saved_files = {}

    try:
        # 转换时间戳为datetime
        data['datetime'] = pd.to_datetime(data['timestamp'], unit='ms')
        data['date'] = data['datetime'].dt.date

        # 按日期分组
        daily_groups = data.groupby('date')
        print(f"📅 发现 {len(daily_groups)} 个交易日")

        for date, daily_data in daily_groups:
            if len(daily_data) < 10:  # 跳过数据太少的日期
                continue

            print(f"📈 绘制 {date} 的图表...")

            # 为每个日期创建图表
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))

            # 上图：当日价格走势和交易点
            ax1.plot(daily_data['datetime'], daily_data['last_price'],
                    label='价格走势', color='blue', alpha=0.7, linewidth=1)

            # 筛选当日的交易
            daily_start = pd.Timestamp.combine(date, pd.Timestamp.min.time())
            daily_end = daily_start + pd.Timedelta(days=1)

            daily_buy_trades = [trade for trade in backtest.trades
                              if trade.side == 'buy' and
                              daily_start <= pd.to_datetime(trade.timestamp, unit='ms') < daily_end]

            daily_sell_trades = [trade for trade in backtest.trades
                               if trade.side == 'sell' and
                               daily_start <= pd.to_datetime(trade.timestamp, unit='ms') < daily_end]

            # 标记当日买入点
            if daily_buy_trades:
                buy_times = [pd.to_datetime(trade.timestamp, unit='ms') for trade in daily_buy_trades]
                buy_prices = [trade.price for trade in daily_buy_trades]
                ax1.scatter(buy_times, buy_prices, color='green', marker='^', s=80,
                           label=f'买入 ({len(daily_buy_trades)})', alpha=0.8, zorder=5)

            # 标记当日卖出点
            if daily_sell_trades:
                sell_times = [pd.to_datetime(trade.timestamp, unit='ms') for trade in daily_sell_trades]
                sell_prices = [trade.price for trade in daily_sell_trades]
                ax1.scatter(sell_times, sell_prices, color='red', marker='v', s=80,
                           label=f'卖出 ({len(daily_sell_trades)})', alpha=0.8, zorder=5)

            ax1.set_title(f'{date} 价格走势与交易点', fontsize=14, fontweight='bold')
            ax1.set_ylabel('价格', fontsize=12)
            ax1.legend()
            ax1.grid(True, alpha=0.3)

            # 下图：当日收益变化
            if backtest.equity_curve:
                # 筛选当日的权益曲线
                daily_equity = [(ts, equity) for ts, equity in backtest.equity_curve
                              if daily_start <= pd.to_datetime(ts, unit='ms') < daily_end]

                if daily_equity:
                    equity_times = [pd.to_datetime(ts, unit='ms') for ts, _ in daily_equity]
                    equity_values = [equity for _, equity in daily_equity]

                    # 计算当日收益率（相对于当日开始）
                    initial_equity = equity_values[0]
                    returns = [(equity - initial_equity) / initial_equity * 100 for equity in equity_values]

                    ax2.plot(equity_times, returns, label='当日收益率', color='purple', linewidth=2)
                    ax2.axhline(y=0, color='black', linestyle='--', alpha=0.5)
                    ax2.fill_between(equity_times, returns, 0, alpha=0.3, color='purple')

                    # 显示当日统计
                    final_return = returns[-1] if returns else 0
                    max_return = max(returns) if returns else 0
                    min_return = min(returns) if returns else 0

                    ax2.text(0.02, 0.98, f'当日收益: {final_return:.2f}%\n最高: {max_return:.2f}%\n最低: {min_return:.2f}%',
                            transform=ax2.transAxes, verticalalignment='top',
                            bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
                else:
                    ax2.text(0.5, 0.5, '当日无权益数据', ha='center', va='center',
                            transform=ax2.transAxes, fontsize=12)

            ax2.set_title(f'{date} 收益率变化', fontsize=14, fontweight='bold')
            ax2.set_xlabel('时间', fontsize=12)
            ax2.set_ylabel('收益率 (%)', fontsize=12)
            ax2.legend()
            ax2.grid(True, alpha=0.3)

            plt.tight_layout()

            # 保存当日图表
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            daily_file = os.path.join(save_dir, f"{filename_prefix}_{date}_{timestamp}.png")
            plt.savefig(daily_file, dpi=300, bbox_inches='tight')
            saved_files[f'daily_{date}'] = daily_file
            plt.close()

            print(f"✅ {date} 图表已保存: {daily_file}")

        print(f"📊 按日期绘图完成，共生成 {len(saved_files)} 个图表")

    except Exception as e:
        print(f"❌ 按日期绘图出错: {e}")
        import traceback
        traceback.print_exc()
        plt.close('all')

    return saved_files

def plot_trading_analysis_simple(backtest: 'QMTOptunaBacktest', data: pd.DataFrame, save_dir: str = "optimization_results", filename_prefix: str = "analysis") -> Dict[str, str]:
    """恢复到简单可靠的绘图版本"""
    print(f"📊 开始绘制图表，数据量: {len(data)} 个点")

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False

    # 创建保存目录
    os.makedirs(save_dir, exist_ok=True)
    saved_files = {}

    try:
        # 1. 绘制价格走势和开仓点图
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))

        # 转换时间戳为datetime
        data['datetime'] = pd.to_datetime(data['timestamp'], unit='ms')

        # 上图：价格走势和交易点
        ax1.plot(data['datetime'], data['last_price'], label='价格走势', color='blue', alpha=0.7, linewidth=1)

        # 标记买入点
        buy_trades = [trade for trade in backtest.trades if trade.side == 'buy']
        if buy_trades:
            buy_times = [pd.to_datetime(trade.timestamp, unit='ms') for trade in buy_trades]
            buy_prices = [trade.price for trade in buy_trades]
            ax1.scatter(buy_times, buy_prices, color='green', marker='^', s=50, label='买入点', alpha=0.8)

        # 标记卖出点
        sell_trades = [trade for trade in backtest.trades if trade.side == 'sell']
        if sell_trades:
            sell_times = [pd.to_datetime(trade.timestamp, unit='ms') for trade in sell_trades]
            sell_prices = [trade.price for trade in sell_trades]
            ax1.scatter(sell_times, sell_prices, color='red', marker='v', s=50, label='卖出点', alpha=0.8)

        ax1.set_title('价格走势与交易点分析', fontsize=14, fontweight='bold')
        ax1.set_ylabel('价格', fontsize=12)
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 下图：收益曲线
        if backtest.equity_curve:
            equity_times = [pd.to_datetime(timestamp, unit='ms') for timestamp, _ in backtest.equity_curve]
            equity_values = [equity for _, equity in backtest.equity_curve]

            # 计算收益率
            initial_equity = equity_values[0] if equity_values else backtest.config.initial_cash
            returns = [(equity - initial_equity) / initial_equity * 100 for equity in equity_values]

            ax2.plot(equity_times, returns, label='收益率曲线', color='purple', linewidth=2)
            ax2.axhline(y=0, color='black', linestyle='--', alpha=0.5)
            ax2.fill_between(equity_times, returns, 0, alpha=0.3, color='purple')

            ax2.set_title('收益率曲线', fontsize=14, fontweight='bold')
            ax2.set_xlabel('时间', fontsize=12)
            ax2.set_ylabel('收益率 (%)', fontsize=12)
            ax2.legend()
            ax2.grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存价格和收益图
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        price_file = os.path.join(save_dir, f"{filename_prefix}_{timestamp}.png")
        plt.savefig(price_file, dpi=300, bbox_inches='tight')
        saved_files['price_and_returns'] = price_file
        plt.close()

        print(f"✅ 图表已保存: {price_file}")

    except Exception as e:
        print(f"❌ 绘图出错: {e}")
        import traceback
        traceback.print_exc()
        plt.close('all')  # 确保清理

    return saved_files

def plot_trading_analysis(backtest: 'QMTOptunaBacktest', data: pd.DataFrame, save_dir: str = "optimization_results", filename_prefix: str = "analysis") -> Dict[str, str]:
    """绘制交易分析图表（优化版本）

    Args:
        backtest: 回测实例
        data: 原始数据
        save_dir: 保存目录
        filename_prefix: 文件名前缀

    Returns:
        Dict[str, str]: 保存的图表文件路径
    """
    print(f"📊 开始绘制图表，数据量: {len(data)} 个点")

    # 数据量过大时进行采样，提高性能
    if len(data) > 5000:
        print(f"⚡ 数据量较大({len(data)}个点)，进行采样优化...")
        # 保留每10个点中的1个，但保留首尾
        step = max(1, len(data) // 2000)  # 最多保留2000个点
        indices = list(range(0, len(data), step))
        if indices[-1] != len(data) - 1:
            indices.append(len(data) - 1)
        data = data.iloc[indices].copy()
        print(f"📉 采样后数据量: {len(data)} 个点")

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False

    # 创建保存目录
    os.makedirs(save_dir, exist_ok=True)

    saved_files = {}

    # 1. 绘制价格走势和开仓点图
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))

    # 转换时间戳为datetime
    data['datetime'] = pd.to_datetime(data['timestamp'], unit='ms')

    # === 简化版：使用连续索引作为x轴，避免复杂映射 ===
    print(f"📊 使用简化时间轴显示")

    # 直接使用数据索引作为x轴
    x_axis = list(range(len(data)))

    # 简化时间标签：只显示几个关键点
    n_labels = min(8, len(data))  # 最多8个标签
    if n_labels <= 1:
        label_indices = [0] if len(data) > 0 else []
    else:
        step = max(1, len(data) // (n_labels - 1))
        label_indices = list(range(0, len(data), step))
        if len(data) > 0 and label_indices[-1] != len(data) - 1:
            label_indices.append(len(data) - 1)

    # 生成简化的时间标签
    time_labels = []
    for i in label_indices:
        if i < len(data):
            dt = data['datetime'].iloc[i]
            time_labels.append(dt.strftime('%H:%M'))

    print(f"📊 时间标签数量: {len(time_labels)}")

    # 上图：价格走势和交易点（使用简化时间轴）
    # 使用简化的x轴坐标
    ax1.scatter(x_axis, data['last_price'], s=2, alpha=0.7, color='blue', label='价格点')

    # 添加移动平均线来显示趋势
    if len(data) > 20:
        window = min(20, len(data) // 10)  # 动态窗口大小
        ma_price = data['last_price'].rolling(window=window, min_periods=1).mean()
        ax1.plot(x_axis, ma_price, label=f'MA{window}趋势线', color='darkblue', alpha=0.8, linewidth=2)

    # 简化交易点显示：直接使用近似位置
    buy_trades = [trade for trade in backtest.trades if trade.side == 'buy']
    if buy_trades and len(buy_trades) <= 100:  # 限制交易点数量，避免性能问题
        buy_times = [pd.to_datetime(trade.timestamp, unit='ms') for trade in buy_trades]
        buy_prices = [trade.price for trade in buy_trades]

        # 简化映射：使用近似位置
        buy_x_coords = []
        data_start_time = data['datetime'].iloc[0]
        data_end_time = data['datetime'].iloc[-1]
        data_time_span = (data_end_time - data_start_time).total_seconds()

        for buy_time in buy_times:
            if data_time_span > 0:
                # 计算相对位置
                relative_pos = (buy_time - data_start_time).total_seconds() / data_time_span
                x_pos = int(relative_pos * (len(data) - 1))
                x_pos = max(0, min(x_pos, len(data) - 1))  # 确保在范围内
                buy_x_coords.append(x_pos)
            else:
                buy_x_coords.append(0)

        ax1.scatter(buy_x_coords, buy_prices, color='green', marker='^', s=120,
                   label=f'买入点 ({len(buy_trades)})', alpha=0.9, edgecolors='darkgreen', linewidth=1.5, zorder=5)

    # 标记卖出点
    sell_trades = [trade for trade in backtest.trades if trade.side == 'sell']
    if sell_trades and len(sell_trades) <= 100:  # 限制交易点数量
        sell_times = [pd.to_datetime(trade.timestamp, unit='ms') for trade in sell_trades]
        sell_prices = [trade.price for trade in sell_trades]

        # 简化映射：使用近似位置
        sell_x_coords = []
        for sell_time in sell_times:
            if data_time_span > 0:
                relative_pos = (sell_time - data_start_time).total_seconds() / data_time_span
                x_pos = int(relative_pos * (len(data) - 1))
                x_pos = max(0, min(x_pos, len(data) - 1))
                sell_x_coords.append(x_pos)
            else:
                sell_x_coords.append(0)

        ax1.scatter(sell_x_coords, sell_prices, color='red', marker='v', s=120,
                   label=f'卖出点 ({len(sell_trades)})', alpha=0.9, edgecolors='darkred', linewidth=1.5, zorder=5)

    ax1.set_title('价格走势与交易点分析 (压缩时间轴)', fontsize=14, fontweight='bold')
    ax1.set_ylabel('价格', fontsize=12)
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 设置简化的时间轴标签
    ax1.set_xticks(label_indices)
    ax1.set_xticklabels(time_labels, rotation=45, ha='right')
    ax1.set_xlabel('交易时间 (简化显示)', fontsize=12)

    # 下图：收益曲线（简化显示）
    if backtest.equity_curve and len(backtest.equity_curve) <= 1000:  # 限制数据量
        equity_times = [pd.to_datetime(timestamp, unit='ms') for timestamp, _ in backtest.equity_curve]
        equity_values = [equity for _, equity in backtest.equity_curve]

        # 计算收益率
        initial_equity = equity_values[0] if equity_values else backtest.config.initial_cash
        returns = [(equity - initial_equity) / initial_equity * 100 for equity in equity_values]

        # 简化显示：直接使用索引
        equity_x = list(range(len(returns)))

        # 使用简化的x轴显示收益曲线
        ax2.step(equity_x, returns, where='post', label='收益率曲线', color='purple', linewidth=2)
        ax2.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        ax2.fill_between(equity_x, returns, 0, alpha=0.3, color='purple', step='post')

        ax2.set_title('收益率曲线', fontsize=14, fontweight='bold')
        ax2.set_xlabel('时间序列', fontsize=12)
        ax2.set_ylabel('收益率 (%)', fontsize=12)
        ax2.legend()
        ax2.grid(True, alpha=0.3)
    else:
        # 如果数据太多，显示简单信息
        ax2.text(0.5, 0.5, '收益数据过多\n已跳过绘制', ha='center', va='center', transform=ax2.transAxes, fontsize=14)
        ax2.set_title('收益率曲线 (已跳过)', fontsize=14, fontweight='bold')

    plt.tight_layout()

    # 保存价格和收益图
    price_file = os.path.join(save_dir, f"{filename_prefix}_price_and_returns.png")
    plt.savefig(price_file, dpi=300, bbox_inches='tight')
    saved_files['price_and_returns'] = price_file
    plt.close()

    # 2. 绘制详细的收益分析图
    if backtest.equity_curve:
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

        # 收益曲线（使用阶梯图）
        ax1.step(equity_times, returns, where='post', color='blue', linewidth=2)
        ax1.fill_between(equity_times, returns, 0, alpha=0.3, color='blue', step='post')
        ax1.set_title('收益率曲线', fontsize=12, fontweight='bold')
        ax1.set_ylabel('收益率 (%)')
        ax1.grid(True, alpha=0.3)
        ax1.axhline(y=0, color='black', linestyle='--', alpha=0.5)

        # 回撤分析
        peak = np.maximum.accumulate(equity_values)
        drawdown = [(equity - peak_val) / peak_val * 100 for equity, peak_val in zip(equity_values, peak)]
        ax2.fill_between(equity_times, drawdown, 0, color='red', alpha=0.3)
        ax2.plot(equity_times, drawdown, color='red', linewidth=1)
        ax2.set_title('回撤分析', fontsize=12, fontweight='bold')
        ax2.set_ylabel('回撤 (%)')
        ax2.grid(True, alpha=0.3)

        # 交易分布
        if backtest.trades:
            pnls = [trade.pnl for trade in backtest.trades if trade.side == 'sell']
            if pnls:
                ax3.hist(pnls, bins=20, alpha=0.7, color='green', edgecolor='black')
                ax3.axvline(x=0, color='red', linestyle='--', alpha=0.7)
                ax3.set_title('交易盈亏分布', fontsize=12, fontweight='bold')
                ax3.set_xlabel('盈亏金额')
                ax3.set_ylabel('频次')
                ax3.grid(True, alpha=0.3)

        # 累计交易次数
        trade_times = [pd.to_datetime(trade.timestamp, unit='ms') for trade in backtest.trades]
        if trade_times:
            trade_counts = list(range(1, len(trade_times) + 1))
            ax4.plot(trade_times, trade_counts, marker='o', markersize=3, linewidth=1, color='orange')
            ax4.set_title('累计交易次数', fontsize=12, fontweight='bold')
            ax4.set_xlabel('时间')
            ax4.set_ylabel('交易次数')
            ax4.grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存详细分析图
        analysis_file = os.path.join(save_dir, f"{filename_prefix}_detailed_analysis.png")
        plt.savefig(analysis_file, dpi=300, bbox_inches='tight')
        saved_files['detailed_analysis'] = analysis_file
        plt.close()

    # 3. 绘制交易统计图
    if backtest.trades:
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 10))

        # 买卖交易数量对比
        buy_count = len([t for t in backtest.trades if t.side == 'buy'])
        sell_count = len([t for t in backtest.trades if t.side == 'sell'])
        ax1.bar(['买入', '卖出'], [buy_count, sell_count], color=['green', 'red'], alpha=0.7)
        ax1.set_title('买卖交易数量', fontsize=12, fontweight='bold')
        ax1.set_ylabel('交易次数')

        # 盈亏交易对比
        sell_trades = [t for t in backtest.trades if t.side == 'sell']
        if sell_trades:
            profit_count = len([t for t in sell_trades if t.pnl > 0])
            loss_count = len([t for t in sell_trades if t.pnl < 0])
            ax2.pie([profit_count, loss_count], labels=['盈利', '亏损'],
                   colors=['green', 'red'], autopct='%1.1f%%', startangle=90)
            ax2.set_title('盈亏交易比例', fontsize=12, fontweight='bold')

        # 每日交易次数
        if trade_times:
            daily_trades = pd.Series(trade_times).dt.date.value_counts().sort_index()
            ax3.bar(range(len(daily_trades)), daily_trades.values, alpha=0.7, color='blue')
            ax3.set_title('每日交易次数', fontsize=12, fontweight='bold')
            ax3.set_xlabel('交易日')
            ax3.set_ylabel('交易次数')
            ax3.set_xticks(range(len(daily_trades)))
            ax3.set_xticklabels([str(date) for date in daily_trades.index], rotation=45)

        # 交易时间分布
        if trade_times:
            hours = [t.hour for t in trade_times]
            hour_counts = pd.Series(hours).value_counts().sort_index()
            ax4.bar(hour_counts.index, hour_counts.values, alpha=0.7, color='orange')
            ax4.set_title('交易时间分布', fontsize=12, fontweight='bold')
            ax4.set_xlabel('小时')
            ax4.set_ylabel('交易次数')
            ax4.set_xticks(range(9, 16))  # 交易时间9:00-15:00

        plt.tight_layout()

        # 保存交易统计图
        stats_file = os.path.join(save_dir, f"{filename_prefix}_trading_stats.png")
        plt.savefig(stats_file, dpi=300, bbox_inches='tight')
        saved_files['trading_stats'] = stats_file
        plt.close()

    return saved_files

def save_optimization_results(results: Dict[str, Any], best_backtest: 'QMTOptunaBacktest' = None,
                            test_data: pd.DataFrame = None, save_dir: str = "optimization_results",
                            save_plots: bool = True, save_config: bool = True) -> str:
    """保存优化结果到本地文件，包括图表分析

    Args:
        results: 优化结果字典
        best_backtest: 最佳配置的回测实例
        test_data: 测试数据
        save_dir: 保存目录

    Returns:
        str: 保存的文件路径
    """
    # 创建保存目录
    print(f"📁 创建保存目录: {save_dir}")
    os.makedirs(save_dir, exist_ok=True)

    # 生成时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    print(f"⏰ 时间戳: {timestamp}")

    # === 新增：条件生成图表分析 ===
    if save_plots and best_backtest is not None and test_data is not None:
        try:
            print("📊 正在生成交易分析图表...")
            print(f"📊 数据量: {len(test_data)} 个点")
            print(f"📊 交易数量: {len(best_backtest.trades)} 个")

            # 生成总体图表
            overall_files = plot_trading_analysis_simple(best_backtest, test_data, save_dir, f"overall_{timestamp}")

            # 生成按日期分组的图表
            daily_files = plot_daily_analysis(best_backtest, test_data, save_dir, f"daily_{timestamp}")

            # 合并所有图表文件
            plot_files = {**overall_files, **daily_files}

            if plot_files:
                print(f"📈 图表已保存: {len(plot_files)} 个文件")
                print(f"  📊 总体图表: {len(overall_files)} 个")
                print(f"  📅 按日期图表: {len(daily_files)} 个")
                for plot_type, file_path in plot_files.items():
                    print(f"  - {plot_type}: {file_path}")
            else:
                print("⚠ 没有生成图表文件")

        except Exception as e:
            print(f"❌ 生成图表时出现错误: {e}")
            import traceback
            traceback.print_exc()
            print(f"📝 继续保存其他结果...")
    elif not save_plots:
        print("📊 跳过图表生成（save_plots=False）")
    else:
        print("⚠ 绘图条件不满足:")
        print(f"  save_plots: {save_plots}")
        print(f"  best_backtest: {best_backtest is not None}")
        print(f"  test_data: {test_data is not None}")

    # 1. 保存完整结果为JSON格式
    json_file = os.path.join(save_dir, f"optuna_results_{timestamp}.json")

    # 准备JSON可序列化的数据
    json_data = {
        'timestamp': timestamp,
        'best_params': results.get('best_params', {}),
        'best_value': results.get('best_value', 0.0),
        'train_results': results.get('train_results', {}),
        'test_results': results.get('test_results', {}),
        'generalization_ratio': results.get('generalization_ratio', 0.0),
        'optimization_summary': {
            'train_return': results.get('train_results', {}).get('total_return', 0.0),
            'test_return': results.get('test_results', {}).get('total_return', 0.0),
            'train_sharpe': results.get('train_results', {}).get('sharpe_ratio', 0.0),
            'test_sharpe': results.get('test_results', {}).get('sharpe_ratio', 0.0),
            'train_max_drawdown': results.get('train_results', {}).get('max_drawdown', 0.0),
            'test_max_drawdown': results.get('test_results', {}).get('max_drawdown', 0.0),
            'train_win_rate': results.get('train_results', {}).get('win_rate', 0.0),
            'test_win_rate': results.get('test_results', {}).get('win_rate', 0.0),
            'train_profit_factor': results.get('train_results', {}).get('profit_factor', 0.0),
            'test_profit_factor': results.get('test_results', {}).get('profit_factor', 0.0)
        },
        # === 新增：Optuna study信息 ===
        'optuna_study_info': {
            'study_name': results.get('study_name', ''),
            'storage_url': results.get('storage_url', ''),
            'n_trials_completed': results.get('n_trials_completed', 0),
            'continued_from': results.get('continued_from', None),
            'additional_trials': results.get('additional_trials', None)
        }
    }

    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(json_data, f, indent=2, ensure_ascii=False)

    # 2. 保存完整结果为Pickle格式（包含配置对象）
    pickle_file = os.path.join(save_dir, f"optuna_results_{timestamp}.pkl")
    with open(pickle_file, 'wb') as f:
        pickle.dump(results, f)

    # 3. 条件生成可直接使用的Python配置文件
    if save_config:
        config_file = os.path.join(save_dir, f"optimized_config_{timestamp}.py")
        generate_config_file(results, config_file, timestamp)
    else:
        print("🐍 跳过Python配置文件生成（save_config=False）")

    # 4. 生成结果摘要文本文件
    summary_file = os.path.join(save_dir, f"optimization_summary_{timestamp}.txt")
    generate_summary_file(results, summary_file, timestamp)

    print(f"\n📁 优化结果已保存到:")
    print(f"  📊 JSON格式: {json_file}")
    print(f"  🔧 Pickle格式: {pickle_file}")
    print(f"  🐍 Python配置: {config_file}")
    print(f"  📝 结果摘要: {summary_file}")

    return save_dir

def generate_config_file(results: Dict[str, Any], file_path: str, timestamp: str):
    """生成可直接使用的Python配置文件"""
    best_params = results.get('best_params', {})
    train_results = results.get('train_results', {})
    test_results = results.get('test_results', {})

    config_content = f'''"""
Optuna优化后的最佳参数配置 - 自动生成
生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
优化目标值: {results.get('best_value', 0.0):.6f}
训练集收益率: {train_results.get('total_return', 0.0):.4f}
测试集收益率: {test_results.get('total_return', 0.0):.4f}
泛化性能: {results.get('generalization_ratio', 0.0):.2f}
"""

from qmt_optuna_backtest import BacktestConfig

def get_optimized_config() -> BacktestConfig:
    """获取自动优化的最佳参数配置"""
    return BacktestConfig(
        # 基础参数
        initial_cash=1000000.0,
        max_position={best_params.get('max_position', 10000)},
        tick_size=0.001,
        commission_rate={best_params.get('commission_rate', 0.0001)},

        # 策略核心参数 (自动优化)
        dt={best_params.get('dt', 2000)},
        window_size={best_params.get('window_size', 300000)},
        order_update_interval={best_params.get('order_update_interval', 20000)},

        # 网格交易参数
        grid_levels={best_params.get('grid_levels', 3)},
        base_quantity={best_params.get('base_quantity', 500)},
        max_spread={best_params.get('max_spread', 0.003)},
        min_spread={best_params.get('min_spread', 0.001)},

        # 原策略参数
        ema_span={best_params.get('ema_span', 0.005)},
        ema_spread={best_params.get('ema_spread', -0.002)},
        intensity_nspread={best_params.get('intensity_nspread', 3)},
        tp_spread={best_params.get('tp_spread', 0.004)},

        # 新增参数
        risk_probility={best_params.get('risk_probility', 0.02)},
        risk_probility_buy={best_params.get('risk_probility_buy', 0.02)},
        risk_probility_sell={best_params.get('risk_probility_sell', 0.02)},
        enter_lot={best_params.get('enter_lot', 5000)},
        grid_spread={best_params.get('grid_spread', 0.006)},
        sl_ratio={best_params.get('sl_ratio', 4.0)},

        # 止盈参数 - 简化版本
        take_profit_levels=[
            ({best_params.get('tp_spread', 0.004)}, 1.0)  # 使用tp_spread值，全平
        ]
    )

def get_performance_summary():
    """获取优化结果的业绩摘要"""
    return {{
        'optimization_timestamp': '{timestamp}',
        'best_objective_value': {results.get('best_value', 0.0):.6f},
        'train_performance': {{
            'total_return': {train_results.get('total_return', 0.0):.4f},
            'sharpe_ratio': {train_results.get('sharpe_ratio', 0.0):.4f},
            'max_drawdown': {train_results.get('max_drawdown', 0.0):.4f},
            'win_rate': {train_results.get('win_rate', 0.0):.4f},
            'profit_factor': {train_results.get('profit_factor', 0.0):.4f},
            'total_trades': {train_results.get('total_trades', 0)}
        }},
        'test_performance': {{
            'total_return': {test_results.get('total_return', 0.0):.4f},
            'sharpe_ratio': {test_results.get('sharpe_ratio', 0.0):.4f},
            'max_drawdown': {test_results.get('max_drawdown', 0.0):.4f},
            'win_rate': {test_results.get('win_rate', 0.0):.4f},
            'profit_factor': {test_results.get('profit_factor', 0.0):.4f},
            'total_trades': {test_results.get('total_trades', 0)}
        }},
        'generalization_ratio': {results.get('generalization_ratio', 0.0):.4f}
    }}
'''

    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(config_content)

def generate_summary_file(results: Dict[str, Any], file_path: str, timestamp: str):
    """生成结果摘要文本文件"""
    best_params = results.get('best_params', {})
    train_results = results.get('train_results', {})
    test_results = results.get('test_results', {})

    summary_content = f"""
=== OPTUNA 优化结果摘要 ===
生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
优化标识: {timestamp}

=== 最佳参数 ===
"""

    for key, value in best_params.items():
        if isinstance(value, float):
            summary_content += f"{key}: {value:.6f}\n"
        else:
            summary_content += f"{key}: {value}\n"

    summary_content += f"""
=== 训练集表现 ===
总收益率: {train_results.get('total_return', 0.0):.4f} ({train_results.get('total_return', 0.0)*100:.2f}%)
夏普比率: {train_results.get('sharpe_ratio', 0.0):.4f}
最大回撤: {train_results.get('max_drawdown', 0.0):.4f} ({train_results.get('max_drawdown', 0.0)*100:.2f}%)
胜率: {train_results.get('win_rate', 0.0):.4f} ({train_results.get('win_rate', 0.0)*100:.2f}%)
盈亏比: {train_results.get('profit_factor', 0.0):.4f}
总交易次数: {train_results.get('total_trades', 0)}
盈利交易: {train_results.get('winning_trades', 0)}
亏损交易: {train_results.get('losing_trades', 0)}

=== 测试集表现 ===
总收益率: {test_results.get('total_return', 0.0):.4f} ({test_results.get('total_return', 0.0)*100:.2f}%)
夏普比率: {test_results.get('sharpe_ratio', 0.0):.4f}
最大回撤: {test_results.get('max_drawdown', 0.0):.4f} ({test_results.get('max_drawdown', 0.0)*100:.2f}%)
胜率: {test_results.get('win_rate', 0.0):.4f} ({test_results.get('win_rate', 0.0)*100:.2f}%)
盈亏比: {test_results.get('profit_factor', 0.0):.4f}
总交易次数: {test_results.get('total_trades', 0)}
盈利交易: {test_results.get('winning_trades', 0)}
亏损交易: {test_results.get('losing_trades', 0)}

=== 泛化性能 ===
泛化比率: {results.get('generalization_ratio', 0.0):.4f}
"""

    if results.get('generalization_ratio', 0.0) > 0.8:
        summary_content += "泛化性能: ✅ 优秀 (测试集表现 > 80%训练集)\n"
    elif results.get('generalization_ratio', 0.0) > 0.5:
        summary_content += "泛化性能: ⚠ 一般 (测试集表现 50%-80%训练集)\n"
    else:
        summary_content += "泛化性能: ❌ 可能过拟合 (测试集表现 < 50%训练集)\n"

    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(summary_content)

def create_config_from_trial(trial: optuna.Trial, opt_config: OptimizationConfig) -> BacktestConfig:
    """从Optuna试验创建回测配置 - 优化版本"""
    
    # 基础参数
    commission_rate = trial.suggest_float('commission_rate', *opt_config.commission_rate_range)
    max_position = trial.suggest_int('max_position', *opt_config.max_position_range)
    
    # === 新增：延时参数配置 ===
    enable_latency = opt_config.enable_latency
    network_latency_mean = 100
    network_latency_std = 30
    system_latency = 20
    exchange_latency = 5
    slippage_rate = 0.0001
    market_impact_factor = 0.00001
    order_timeout = 30000
    max_pending_orders = 10
    
    if enable_latency:
        network_latency_mean = trial.suggest_int('network_latency_mean', *opt_config.network_latency_mean_range)
        network_latency_std = trial.suggest_int('network_latency_std', *opt_config.network_latency_std_range)
        system_latency = trial.suggest_int('system_latency', *opt_config.system_latency_range)
        exchange_latency = trial.suggest_int('exchange_latency', *opt_config.exchange_latency_range)
        slippage_rate = trial.suggest_float('slippage_rate', *opt_config.slippage_rate_range)
        market_impact_factor = trial.suggest_float('market_impact_factor', *opt_config.market_impact_factor_range)
        order_timeout = trial.suggest_int('order_timeout', *opt_config.order_timeout_range)
        max_pending_orders = trial.suggest_int('max_pending_orders', *opt_config.max_pending_orders_range)
    
    # 策略参数（基于优化后的qmt_mm.py）
    dt = trial.suggest_int('dt', *opt_config.dt_range)
    window_size = trial.suggest_int('window_size', *opt_config.window_size_range)
    
    # 挂单更新频率
    order_update_interval = trial.suggest_int('order_update_interval', *opt_config.order_update_interval_range)
    
    # 网格交易参数
    grid_levels = trial.suggest_int('grid_levels', *opt_config.grid_levels_range)
    base_quantity = trial.suggest_int('base_quantity', *opt_config.base_quantity_range)
    max_spread = trial.suggest_float('max_spread', *opt_config.max_spread_range)
    min_spread = trial.suggest_float('min_spread', *opt_config.min_spread_range)
    
    # 确保min_spread < max_spread
    if min_spread >= max_spread:
        min_spread = max_spread * 0.3  # 修改为30%而不是50%
    
    # === 优化7：动态网格间距参数 ===
    base_grid_spread = trial.suggest_float('base_grid_spread', *opt_config.base_grid_spread_range)
    min_grid_spread = trial.suggest_float('min_grid_spread', *opt_config.min_grid_spread_range)
    max_grid_spread = trial.suggest_float('max_grid_spread', *opt_config.max_grid_spread_range)
    volatility_window = trial.suggest_int('volatility_window', *opt_config.volatility_window_range)
    
    # 确保网格间距的逻辑关系
    if min_grid_spread >= max_grid_spread:
        min_grid_spread = max_grid_spread * 0.3
    if base_grid_spread < min_grid_spread:
        base_grid_spread = min_grid_spread
    elif base_grid_spread > max_grid_spread:
        base_grid_spread = max_grid_spread
    
    # 原策略参数
    ema_span = trial.suggest_float('ema_span', *opt_config.ema_span_range)
    ema_spread = trial.suggest_float('ema_spread', *opt_config.ema_spread_range)
    intensity_nspread = trial.suggest_int('intensity_nspread', *opt_config.intensity_nspread_range)
    tp_spread = trial.suggest_float('tp_spread', *opt_config.tp_spread_range)
    
    # 止盈参数 - 分层止盈已注释
    # tp_level1_pct = trial.suggest_float('tp_level1_pct', *opt_config.tp_level1_pct_range)
    # tp_level1_ratio = trial.suggest_float('tp_level1_ratio', *opt_config.tp_level1_ratio_range)
    # tp_level2_pct = trial.suggest_float('tp_level2_pct', *opt_config.tp_level2_pct_range)
    # tp_level2_ratio = trial.suggest_float('tp_level2_ratio', *opt_config.tp_level2_ratio_range)
    # tp_level3_pct = trial.suggest_float('tp_level3_pct', *opt_config.tp_level3_pct_range)

    # 使用简单的一层止盈
    take_profit_levels = [
        (tp_spread, 1.0)  # 使用tp_spread作为止盈价差，全平
    ]
    
    # === 新增：风险偏好参数 ===
    risk_probility = trial.suggest_float('risk_probility', *opt_config.risk_probility_range)
    risk_probility_buy = trial.suggest_float('risk_probility_buy', *opt_config.risk_probility_buy_range)
    risk_probility_sell = trial.suggest_float('risk_probility_sell', *opt_config.risk_probility_sell_range)
    
    # === 新增：网格参数（与qmt_mm.py保持一致）===
    enter_lot = trial.suggest_int('enter_lot', *opt_config.enter_lot_range)
    grid_spread = trial.suggest_float('grid_spread', *opt_config.grid_spread_range)
    # 网格参数使用固定值，与原始策略保持一致
    grid_spreadnet_param = [1, 1.5, 2, 2.5]
    grid_spreadqty_param = [1, 1, 2, 2]  # 与qmt_mm.py和BacktestConfig保持一致
    
    # === 新增：止损参数 ===
    sl_ratio = trial.suggest_float('sl_ratio', *opt_config.sl_ratio_range)
    
    return BacktestConfig(
        initial_cash=opt_config.initial_cash,
        max_position=max_position,
        tick_size=opt_config.tick_size,
        commission_rate=commission_rate,
        # === 新增：延时参数 ===
        enable_latency=enable_latency,
        network_latency_mean=network_latency_mean,
        network_latency_std=network_latency_std,
        system_latency=system_latency,
        exchange_latency=exchange_latency,
        slippage_rate=slippage_rate,
        market_impact_factor=market_impact_factor,
        order_timeout=order_timeout,
        max_pending_orders=max_pending_orders,
        dt=dt,
        window_size=window_size,
        order_update_interval=order_update_interval,
        grid_levels=grid_levels,
        base_quantity=base_quantity,
        max_spread=max_spread,
        min_spread=min_spread,
        # === 优化7：动态网格间距参数 ===
        base_grid_spread=base_grid_spread,
        min_grid_spread=min_grid_spread,
        max_grid_spread=max_grid_spread,
        volatility_window=volatility_window,
        ema_span=ema_span,
        ema_spread=ema_spread,
        intensity_nspread=intensity_nspread,
        tp_spread=tp_spread,
        take_profit_levels=take_profit_levels,
        # === 新增：风险偏好参数 ===
        risk_probility=risk_probility,
        risk_probility_buy=risk_probility_buy,
        risk_probility_sell=risk_probility_sell,
        # === 新增：网格参数（与qmt_mm.py保持一致）===
        enter_lot=enter_lot,
        grid_spread=grid_spread,
        grid_spreadnet_param=grid_spreadnet_param,
        grid_spreadqty_param=grid_spreadqty_param,
        sl_ratio=sl_ratio
    )

def objective_function(trial: optuna.Trial, data: pd.DataFrame, opt_config: OptimizationConfig) -> float:
    """Optuna目标函数"""
    try:
        # 创建配置
        config = create_config_from_trial(trial, opt_config)
        
        # 运行回测
        backtest = QMTOptunaBacktest(config)
        results = backtest.run_backtest(data)
        
        # 多目标优化：总收益率 * 夏普比率 * (1 - 最大回撤)
        total_return = results['total_return']
        sharpe_ratio = results['sharpe_ratio']
        max_drawdown = results['max_drawdown']
        
        # 惩罚过高的回撤
        if max_drawdown > 0.3:  # 回撤超过30%严重惩罚
            return -1.0
        
        # 组合目标函数
        objective_value = total_return / max_drawdown
        
        # 记录中间结果
        trial.set_user_attr('total_return', total_return)
        trial.set_user_attr('sharpe_ratio', sharpe_ratio)
        trial.set_user_attr('max_drawdown', max_drawdown)
        trial.set_user_attr('total_trades', results['total_trades'])
        trial.set_user_attr('win_rate', results['win_rate'])
        
        return objective_value
        
    except Exception as e:
        print(f"试验失败: {e}")
        return -1.0

def get_data_files(data_dir: str = "backtest_data/merged") -> List[str]:
    """获取所有数据文件，按时间顺序排序"""
    files = glob.glob(f"{data_dir}/*.csv")
    
    # 从文件名中提取日期进行排序，确保时间顺序
    def extract_date_from_filename(filename):
        """从文件名中提取日期，如 sh513120_20240902.csv -> 20240902"""
        import re
        match = re.search(r'(\d{8})', os.path.basename(filename))
        return match.group(1) if match else '00000000'
    
    # 按日期排序，确保时间顺序
    files.sort(key=extract_date_from_filename)
    
    print(f"数据文件时间顺序:")
    for i, f in enumerate(files):
        date = extract_date_from_filename(f)
        print(f"  {i+1}. {os.path.basename(f)} (日期: {date})")
    
    return files

def split_train_test_files(files: List[str], train_ratio: float = 0.7) -> Tuple[List[str], List[str]]:
    """按时间顺序分割训练集和测试集文件
    
    重要：训练集日期必须早于测试集日期，模拟真实交易场景
    """
    if len(files) == 0:
        raise ValueError("没有找到数据文件")
    
    n_train = int(len(files) * train_ratio)
    if n_train == 0:
        n_train = 1  # 至少要有一个训练文件
    if n_train >= len(files):
        n_train = len(files) - 1  # 至少要有一个测试文件
    
    # 按时间顺序分割：前面的文件作为训练集，后面的文件作为测试集
    train_files = files[:n_train]
    test_files = files[n_train:]
    
    # 验证时间顺序
    def extract_date_from_filename(filename):
        import re
        match = re.search(r'(\d{8})', os.path.basename(filename))
        return match.group(1) if match else '00000000'
    
    if train_files and test_files:
        last_train_date = extract_date_from_filename(train_files[-1])
        first_test_date = extract_date_from_filename(test_files[0])
        
        print(f"\n=== 时间顺序验证 ===")
        print(f"训练集最后日期: {last_train_date}")
        print(f"测试集开始日期: {first_test_date}")
        
        if last_train_date >= first_test_date:
            print("⚠️  警告：时间顺序可能有问题！")
        else:
            print("✓ 时间顺序正确：训练集日期 < 测试集日期")
    
    return train_files, test_files

def load_multiple_files(file_paths: List[str]) -> pd.DataFrame:
    """加载多个文件并合并"""
    all_data = []
    
    for file_path in file_paths:
        print(f"加载文件: {file_path}")
        backtest = QMTOptunaBacktest(BacktestConfig())
        data = backtest.load_data(file_path)
        all_data.append(data)
    
    if all_data:
        combined_data = pd.concat(all_data, ignore_index=True)
        combined_data = combined_data.sort_values('timestamp').reset_index(drop=True)
        print(f"合并后数据: {len(combined_data)} 行")
        return combined_data
    else:
        raise ValueError("没有找到有效的数据文件")

def list_existing_studies() -> List[str]:
    """列出现有的Optuna研究"""
    study_dir = "optuna_studies"
    if not os.path.exists(study_dir):
        return []

    studies = []
    for file in os.listdir(study_dir):
        if file.endswith('.db'):
            study_name = file[:-3]  # 移除.db后缀
            studies.append(study_name)

    return sorted(studies, reverse=True)  # 按时间倒序

def get_latest_study() -> str:
    """获取最新的study名称"""
    studies = list_existing_studies()
    if not studies:
        raise FileNotFoundError("没有找到任何现有的study")
    return studies[0]  # 已经按时间倒序排列，第一个就是最新的

def load_latest_study() -> optuna.Study:
    """加载最新的Optuna研究"""
    latest_study_name = get_latest_study()
    print(f"🔄 自动加载最新的study: {latest_study_name}")
    return load_existing_study(latest_study_name)

def load_existing_study(study_name: str) -> optuna.Study:
    """加载现有的Optuna研究"""
    study_dir = "optuna_studies"
    storage_url = f"sqlite:///{study_dir}/{study_name}.db"

    if not os.path.exists(f"{study_dir}/{study_name}.db"):
        raise FileNotFoundError(f"Study文件不存在: {study_name}.db")

    study = optuna.load_study(
        study_name=study_name,
        storage=storage_url
    )

    print(f"📊 已加载study: {study_name}")
    print(f"💾 存储位置: {storage_url}")
    print(f"🔢 已完成试验数: {len(study.trials)}")
    print(f"🏆 当前最佳值: {study.best_value:.6f}")

    return study

def continue_latest_optimization(data_dir: str, opt_config: OptimizationConfig,
                               additional_trials: int = 20, train_ratio: float = 0.7) -> Dict[str, Any]:
    """自动继续最新的优化研究"""
    try:
        latest_study_name = get_latest_study()
        print(f"🔄 自动继续最新的优化研究: {latest_study_name}")
        return continue_optimization(latest_study_name, data_dir, opt_config, additional_trials, train_ratio)
    except FileNotFoundError:
        print("📭 没有找到现有的study，将创建新的优化研究")
        return optimize_parameters_with_validation(data_dir, opt_config, train_ratio)

def continue_optimization(study_name: str, data_dir: str, opt_config: OptimizationConfig,
                         additional_trials: int = 20, train_ratio: float = 0.7) -> Dict[str, Any]:
    """继续现有的优化研究"""
    print(f"🔄 继续优化现有研究: {study_name}")
    print(f"📈 计划增加试验数: {additional_trials}")

    # 加载现有study
    study = load_existing_study(study_name)

    # 加载数据（与原始优化相同的流程）
    all_files = get_data_files(data_dir)
    train_files, test_files = split_train_test_files(all_files, train_ratio)
    train_data = load_multiple_files(train_files)

    # 继续优化
    print(f"\n=== 继续参数优化 ===")
    study.optimize(
        lambda trial: objective_function(trial, train_data, opt_config),
        n_trials=additional_trials,
        n_jobs=opt_config.n_jobs,
        timeout=opt_config.timeout,
        show_progress_bar=True
    )

    print(f"\n=== 优化完成 ===")
    print(f"总试验数: {len(study.trials)}")
    print(f"最佳值: {study.best_value:.6f}")

    # 返回更新后的结果
    best_config = create_config_from_trial(study.best_trial, opt_config)

    # 在测试集上验证
    test_data = load_multiple_files(test_files)
    test_backtest = QMTOptunaBacktest(best_config)
    test_results = test_backtest.run_backtest(test_data)

    # 在训练集上运行最佳配置
    train_backtest = QMTOptunaBacktest(best_config)
    train_results = train_backtest.run_backtest(train_data)

    # 计算泛化性能
    train_return = train_results['total_return']
    test_return = test_results['total_return']
    generalization_ratio = test_return / train_return if train_return != 0 else 0

    # 获取storage_url
    study_dir = "optuna_studies"
    storage_url = f"sqlite:///{study_dir}/{study_name}.db"

    results = {
        'best_params': study.best_params,
        'best_value': study.best_value,
        'best_config': best_config,
        'train_results': train_results,
        'test_results': test_results,
        'generalization_ratio': generalization_ratio,
        'study': study,
        'study_name': study.study_name,
        'storage_url': storage_url,
        'n_trials_completed': len(study.trials),
        'train_files': train_files,
        'test_files': test_files,
        'continued_from': study_name,
        'additional_trials': additional_trials
    }

    # === 新增：自动保存继续优化的结果到本地，包括图表分析 ===
    print(f"\n" + "="*60)
    print(f"📁 开始保存继续优化的结果...")
    print(f"📊 最佳参数数量: {len(study.best_params)}")
    print(f"📈 最佳目标值: {study.best_value:.6f}")
    print(f"🔢 完成试验数: {len(study.trials)}")
    print(f"🔄 继续自: {study_name}")
    print(f"➕ 新增试验: {additional_trials}")

    try:
        # 保存结果和生成图表（使用已有的test_backtest实例）
        print(f"💾 调用保存函数...")
        save_dir = save_optimization_results(
            results,
            best_backtest=test_backtest,
            test_data=test_data,
            save_plots=True,  # 恢复绘图功能
            save_config=True  # 保留配置文件生成
        )
        print(f"✅ 继续优化结果已保存到: {save_dir}")

        # 检查保存的文件
        import os
        if os.path.exists(save_dir):
            files = os.listdir(save_dir)
            print(f"📂 生成文件数量: {len(files)}")
            for file in sorted(files)[:5]:  # 显示前5个文件
                print(f"  📄 {file}")
            if len(files) > 5:
                print(f"  ... 还有 {len(files)-5} 个文件")

        # 添加保存信息到返回结果
        results['save_directory'] = save_dir
        results['detailed_backtest'] = test_backtest

    except Exception as e:
        print(f"❌ 保存继续优化结果时出现错误: {e}")
        import traceback
        traceback.print_exc()
        print(f"📝 错误详情已输出，请检查上述信息")

    print(f"="*60)

    return results

def optimize_parameters_with_validation(data_dir: str, opt_config: OptimizationConfig,
                                      train_ratio: float = 0.7, continue_study: str = None,
                                      auto_continue: bool = False) -> Dict[str, Any]:
    """带验证的参数优化主函数

    Args:
        data_dir: 数据目录
        opt_config: 优化配置
        train_ratio: 训练集比例
        continue_study: 要继续的study名称，如果为None则创建新study
        auto_continue: 是否自动继续最新的study
    """

    # 如果启用自动继续，则尝试继续最新的study
    if auto_continue:
        return continue_latest_optimization(data_dir, opt_config,
                                          additional_trials=opt_config.n_trials,
                                          train_ratio=train_ratio)

    # 如果指定了要继续的study，则使用继续优化功能
    if continue_study:
        return continue_optimization(continue_study, data_dir, opt_config,
                                   additional_trials=opt_config.n_trials,
                                   train_ratio=train_ratio)

    print(f"开始优化参数，数据目录: {data_dir}")
    print(f"训练集比例: {train_ratio:.0%}, 测试集比例: {1-train_ratio:.0%}")

    # 显示现有的studies
    existing_studies = list_existing_studies()
    if existing_studies:
        print(f"\n📚 发现现有的优化研究 ({len(existing_studies)} 个):")
        for i, study_name in enumerate(existing_studies[:5]):  # 只显示最近5个
            print(f"  {i+1}. {study_name}")
        if len(existing_studies) > 5:
            print(f"  ... 还有 {len(existing_studies)-5} 个")
        print("💡 提示：可以使用 continue_study 参数继续现有研究")
    
    # 获取所有数据文件
    all_files = get_data_files(data_dir)
    print(f"找到 {len(all_files)} 个数据文件")
    
    # 分割训练集和测试集
    train_files, test_files = split_train_test_files(all_files, train_ratio)
    
    print(f"\n=== 数据分割 ===")
    print(f"训练集文件 ({len(train_files)} 个):")
    for f in train_files:
        print(f"  {f}")
    print(f"测试集文件 ({len(test_files)} 个):")
    for f in test_files:
        print(f"  {f}")
    
    # 加载训练数据
    print(f"\n=== 加载训练数据 ===")
    train_data = load_multiple_files(train_files)
    
    # === 新增：创建持久化的Optuna研究 ===
    # 创建study存储目录
    study_dir = "optuna_studies"
    os.makedirs(study_dir, exist_ok=True)

    # 生成study名称（基于当前时间和配置）
    study_name = f"etf_mm_optimization_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    storage_url = f"sqlite:///{study_dir}/{study_name}.db"

    print(f"📊 创建Optuna研究: {study_name}")
    print(f"💾 存储位置: {storage_url}")

    # 创建或加载study
    study = optuna.create_study(
        study_name=study_name,
        storage=storage_url,
        direction='maximize',
        sampler=TPESampler(seed=42),
        pruner=MedianPruner(n_startup_trials=10, n_warmup_steps=5),
        load_if_exists=True  # 如果存在则加载
    )
    
    # 在训练集上运行优化
    print(f"\n=== 开始参数优化 (训练集) ===")
    study.optimize(
        lambda trial: objective_function(trial, train_data, opt_config),
        n_trials=opt_config.n_trials,
        n_jobs=opt_config.n_jobs,
        timeout=opt_config.timeout,
        show_progress_bar=True
    )
    
    # 获取最佳参数
    best_params = study.best_params
    best_value = study.best_value
    
    print(f"\n=== 训练集优化结果 ===")
    print(f"最佳目标值: {best_value:.4f}")
    print(f"最佳参数:")
    for key, value in best_params.items():
        print(f"  {key}: {value}")
    
    # 在训练集上运行最佳配置
    best_config = create_config_from_trial(study.best_trial, opt_config)
    train_backtest = QMTOptunaBacktest(best_config)
    train_results = train_backtest.run_backtest(train_data)
    
    print(f"\n=== 训练集表现 ===")
    print(f"总收益率: {train_results['total_return']:.4f}")
    print(f"夏普比率: {train_results['sharpe_ratio']:.4f}")
    print(f"最大回撤: {train_results['max_drawdown']:.4f}")
    print(f"总交易次数: {train_results['total_trades']}")
    print(f"胜率: {train_results.get('win_rate', 0):.4f} ({train_results.get('win_rate', 0)*100:.2f}%)")
    print(f"盈亏比: {train_results.get('profit_factor', 0):.4f}")

    # === 新增：训练集交易分类统计 ===
    if 'trade_reason_stats' in train_results:
        reason_stats = train_results['trade_reason_stats']
        print(f"止损率: {reason_stats.get('stop_loss_rate', 0):.4f} ({reason_stats.get('stop_loss_rate', 0)*100:.2f}%)")
        print(f"止盈率: {reason_stats.get('take_profit_rate', 0):.4f} ({reason_stats.get('take_profit_rate', 0)*100:.2f}%)")
    
    # 在测试集上验证
    print(f"\n=== 加载测试数据 ===")
    test_data = load_multiple_files(test_files)
    
    print(f"\n=== 测试集验证 ===")
    test_backtest = QMTOptunaBacktest(best_config)
    test_results = test_backtest.run_backtest(test_data)
    
    print(f"\n=== 测试集表现 ===")
    print(f"总收益率: {test_results['total_return']:.4f}")
    print(f"夏普比率: {test_results['sharpe_ratio']:.4f}")
    print(f"最大回撤: {test_results['max_drawdown']:.4f}")
    print(f"总交易次数: {test_results['total_trades']}")
    print(f"胜率: {test_results.get('win_rate', 0):.4f} ({test_results.get('win_rate', 0)*100:.2f}%)")
    print(f"盈亏比: {test_results.get('profit_factor', 0):.4f}")

    # === 新增：测试集交易分类统计 ===
    if 'trade_reason_stats' in test_results:
        reason_stats = test_results['trade_reason_stats']
        print(f"止损率: {reason_stats.get('stop_loss_rate', 0):.4f} ({reason_stats.get('stop_loss_rate', 0)*100:.2f}%)")
        print(f"止盈率: {reason_stats.get('take_profit_rate', 0):.4f} ({reason_stats.get('take_profit_rate', 0)*100:.2f}%)")

        if 'reason_counts' in reason_stats:
            counts = reason_stats['reason_counts']
            print(f"止损次数: {counts.get('stop_loss', 0)}, 止盈次数: {counts.get('take_profit_1', 0) + counts.get('take_profit_2_full', 0)}")
    
    # 计算泛化性能
    train_return = train_results['total_return']
    test_return = test_results['total_return']
    generalization_ratio = test_return / train_return if train_return != 0 else 0
    
    print(f"\n=== 泛化性能分析 ===")
    print(f"训练集收益率: {train_return:.4f}")
    print(f"测试集收益率: {test_return:.4f}")
    print(f"泛化比例: {generalization_ratio:.4f}")
    
    if generalization_ratio > 0.8:
        print("✓ 泛化性能良好 (测试集表现 >= 80%训练集)")
    elif generalization_ratio > 0.5:
        print("⚠ 泛化性能一般 (测试集表现 50%-80%训练集)")
    else:
        print("✗ 可能存在过拟合 (测试集表现 < 50%训练集)")
    
    # 构建结果字典
    results = {
        'best_params': best_params,
        'best_value': best_value,
        'best_config': best_config,
        'train_results': train_results,
        'test_results': test_results,
        'generalization_ratio': generalization_ratio,
        'study': study,
        'study_name': study.study_name,  # 新增：study名称
        'storage_url': storage_url,  # 新增：存储位置（使用我们创建时的变量）
        'n_trials_completed': len(study.trials),  # 新增：完成的试验数
        'train_files': train_files,
        'test_files': test_files
    }

    # === 新增：自动保存最佳结果到本地，包括图表分析 ===
    print(f"\n" + "="*60)
    print(f"📁 开始保存优化结果...")
    print(f"📊 最佳参数数量: {len(best_params)}")
    print(f"📈 最佳目标值: {best_value:.6f}")
    print(f"🔢 完成试验数: {len(study.trials)}")

    try:
        # 保存结果和生成图表（使用已有的test_backtest实例）
        print(f"💾 调用保存函数...")
        save_dir = save_optimization_results(
            results,
            best_backtest=test_backtest,
            test_data=test_data,
            save_plots=True,  # 恢复绘图功能
            save_config=True  # 保留配置文件生成
        )
        print(f"✅ 优化结果已保存到: {save_dir}")

        # 检查保存的文件
        import os
        if os.path.exists(save_dir):
            files = os.listdir(save_dir)
            print(f"📂 生成文件数量: {len(files)}")
            for file in sorted(files)[:5]:  # 显示前5个文件
                print(f"  📄 {file}")
            if len(files) > 5:
                print(f"  ... 还有 {len(files)-5} 个文件")

        # 添加保存信息到返回结果
        results['save_directory'] = save_dir
        results['detailed_backtest'] = test_backtest

    except Exception as e:
        print(f"❌ 保存结果时出现错误: {e}")
        import traceback
        traceback.print_exc()
        print(f"📝 错误详情已输出，请检查上述信息")

    print(f"="*60)

    return results

def run_optimization_example():
    """运行优化示例 - 带训练/测试集分离（支持延时模拟）"""
    # 优化配置
    opt_config = OptimizationConfig(
        sl_ratio_range=(0.8, 1.2),
        n_trials=50,
        # 其他参数范围可用默认
    )
    
    # 运行带验证的优化
    data_dir = "backtest_data/merged"
    if not os.path.exists(data_dir):
        print(f"数据目录不存在: {data_dir}")
        return
    
    results = optimize_parameters_with_validation(data_dir, opt_config, train_ratio=0.7)
    
    # 输出结果汇总
    print(f"\n=== 最终结果汇总 ===")
    print(f"训练集收益率: {results['train_results']['total_return']:.4f}")
    print(f"训练集夏普比率: {results['train_results']['sharpe_ratio']:.4f}")
    print(f"训练集最大回撤: {results['train_results']['max_drawdown']:.4f}")
    print(f"训练集交易次数: {results['train_results']['total_trades']}")
    
    print(f"\n测试集收益率: {results['test_results']['total_return']:.4f}")
    print(f"测试集夏普比率: {results['test_results']['sharpe_ratio']:.4f}")
    print(f"测试集最大回撤: {results['test_results']['max_drawdown']:.4f}")
    print(f"测试集交易次数: {results['test_results']['total_trades']}")
    
    print(f"\n泛化比例: {results['generalization_ratio']:.4f}")
    
    print(f"\n=== 最佳参数 ===")
    config = results['best_config']
    print(f"max_position: {config.max_position}")
    print(f"base_quantity: {config.base_quantity}")
    print(f"order_update_interval: {config.order_update_interval}ms ({config.order_update_interval/1000:.1f}秒)")
    print(f"tp_spread: {config.tp_spread}")
    print(f"dt: {config.dt}")
    print(f"window_size: {config.window_size}")
    print(f"max_spread: {config.max_spread}")
    print(f"min_spread: {config.min_spread}")
    print(f"ema_span: {config.ema_span}")
    print(f"commission_rate: {config.commission_rate}")

    return results

if __name__ == "__main__":
    run_optimization_example()