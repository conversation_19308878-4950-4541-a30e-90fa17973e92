#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自动继续功能
"""

def test_auto_continue_functions():
    """测试自动继续相关功能"""
    try:
        from qmt_optuna_backtest import list_existing_studies, get_latest_study
        print('✅ 自动读取功能导入成功')
        
        # 测试列出studies
        studies = list_existing_studies()
        print(f'📚 当前studies数量: {len(studies)}')
        
        if studies:
            print(f'现有studies (最近3个):')
            for study in studies[:3]:
                print(f'  - {study}')
            
            try:
                latest = get_latest_study()
                print(f'✅ 最新study: {latest}')
            except Exception as e:
                print(f'❌ 获取最新study失败: {e}')
        else:
            print('📭 没有现有studies')
        
        print('\n📋 自动继续功能说明:')
        print('1. 🔍 自动检测现有studies')
        print('2. 📅 按时间排序，获取最新的')
        print('3. 🔄 自动继续最新的优化')
        print('4. 📊 保持所有历史记录')
        
        return True
        
    except Exception as e:
        print(f'❌ 错误: {e}')
        return False

if __name__ == "__main__":
    print("🧪 测试自动继续功能...")
    
    if test_auto_continue_functions():
        print("\n✅ 测试通过！")
        print("\n🚀 使用方法:")
        print("1. 运行 python run_optuna_optimization.py")
        print("   - 会自动检测现有studies")
        print("   - 可选择继续或创建新的")
        print()
        print("2. 运行 python auto_continue_optimization.py")
        print("   - 自动继续最新的study")
        print("   - 如果没有则创建新的")
        print()
        print("3. 运行 python manage_optuna_studies.py list")
        print("   - 查看所有现有studies")
    else:
        print("\n❌ 测试失败")
