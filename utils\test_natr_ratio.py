import ccxt  
from math import sqrt
import pandas as pd  

# 初始化交易所  
exchange = ccxt.binance({  
    'rateLimit': 1200,  
    'enableRateLimit': True,  
})  

# 获取K线数据  
def fetch_ohlcv(symbol, timeframe, limit=100):  
    ohlcv = exchange.fetch_ohlcv(symbol, timeframe, limit=limit)  
    df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])  
    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')  
    return df  

# 计算 ATR  
def calculate_atr(df, period=14):  
    df['tr'] = df[['high', 'low', 'close']].apply(  
        lambda row: max(row['high'] - row['low'], abs(row['high'] - row['close']), abs(row['low'] - row['close'])),  
        axis=1  
    )  
    df['atr'] = df['tr'].rolling(window=period).mean()  
    return df  

# 计算 NATR  
def calculate_natr(df, period=24):  
    df = calculate_atr(df, period)  
    df['natr'] = (df['atr'] / df['close']) 
    return df  

# 获取 1 天和 30 分钟的 NATR  
symbol = 'TRUMP/USDT'  
daily_df = fetch_ohlcv(symbol, '1h', limit=50)  
daily_df = calculate_natr(daily_df,2)  

thirty_min_df = fetch_ohlcv(symbol, '3m', limit=50)  
thirty_min_df = calculate_natr(thirty_min_df, 5)  

# 获取最新的 NATR 值  
daily_natr = daily_df['natr'].iloc[-1]  
thirty_min_natr = thirty_min_df['natr'].iloc[-1]  

# 计算比值  
natr_ratio = sqrt(thirty_min_natr/daily_natr)

print(f"1天NATR: {daily_natr:.4f}")  
print(f"30分钟NATR: {thirty_min_natr:.4f}")  
print(f"NATR比值: {natr_ratio:.4f}")  