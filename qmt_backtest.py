# encoding: utf-8
"""
基于qmt_mm.py的AS模型ETF T+0做市策略回测系统（考虑交易延时）
"""

import pandas as pd
import numpy as np
import math
import os
import random
from typing import List, Tuple, Dict, Optional
import datetime
from collections import deque

class EmpiricalIntensityEstimator:
    """经验强度估算器 - 简化版本用于回测"""
    
    class LimitOrderTracker:
        def __init__(self, start_ts: int, order_price: float):
            self.start_ts = start_ts
            self.order_price = order_price

    class Fill:
        def is_order_filled(self, filled_price: float, order_price: float) -> bool:
            raise NotImplementedError()

    class SellFill(Fill):
        def is_order_filled(self, filled_price: float, order_price: float) -> bool:
            return filled_price > order_price

    class BuyFill(Fill):
        def is_order_filled(self, filled_price: float, order_price: float) -> bool:
            return filled_price < order_price

    def __init__(self, spread: float, spread_direction: float, dt: int):
        self.spread = spread
        self.dt = dt
        self.initializing = True
        self.last_price = float('nan')
        self.last_limit_order_inserted = 0
        self.live_trackers: List['EmpiricalIntensityEstimator.LimitOrderTracker'] = []
        self.live_trackers_start_time_sum = 0
        self.finished_trackers: List[Tuple[int, int]] = []
        self.finished_trackers_wait_time_sum = 0
        self.fill_comp = self.SellFill() if spread_direction > 0 else self.BuyFill()

    def on_tick(self, ref_price: float, fill_price: float, ts: int, window_start: int):
        if self.initializing:
            self.initializing = False
            self.last_limit_order_inserted = ts - self.dt

        while self.last_limit_order_inserted + self.dt < ts:
            self.last_limit_order_inserted += self.dt
            self.live_trackers.append(
                self.LimitOrderTracker(self.last_limit_order_inserted, self.last_price + self.spread)
            )
            self.live_trackers_start_time_sum += self.last_limit_order_inserted

        if self.last_limit_order_inserted + self.dt == ts:
            self.last_limit_order_inserted = ts
            self.live_trackers.append(self.LimitOrderTracker(ts, ref_price + self.spread))
            self.live_trackers_start_time_sum += ts

        self.last_price = ref_price

        for tracker in self.live_trackers[:]:
            if window_start > tracker.start_ts:
                self.live_trackers.remove(tracker)
                self.live_trackers_start_time_sum -= tracker.start_ts
                continue

            if self.fill_comp.is_order_filled(fill_price, tracker.order_price):
                self.live_trackers.remove(tracker)
                self.live_trackers_start_time_sum -= tracker.start_ts
                duration = ts - tracker.start_ts
                self.finished_trackers.append((tracker.start_ts, duration))
                self.finished_trackers_wait_time_sum += duration

    def estimate_intensity(self, ts: int, window_start: int) -> float:
        for tracker in self.finished_trackers[:]:
            if tracker[0] < window_start:
                self.finished_trackers.remove(tracker)
                self.finished_trackers_wait_time_sum -= tracker[1]

        if self.live_trackers and ts != self.live_trackers[-1].start_ts:
            for tracker in self.live_trackers[:]:
                if window_start > tracker.start_ts:
                    self.live_trackers.remove(tracker)
                    self.live_trackers_start_time_sum -= tracker.start_ts

        if not self.live_trackers:
            return 0.0

        numerator = self.dt * len(self.finished_trackers)
        denominator = (
            len(self.live_trackers) * ts
            - self.live_trackers_start_time_sum
            + self.finished_trackers_wait_time_sum
        )

        if denominator == 0:
            return 0.0
        return max(float(numerator) / denominator, 1e-7)


class AbstractAkSolver:
    def __init__(self, spread_specification: np.ndarray):
        self.spread_specification = np.abs(spread_specification)

    def solve_ak(self, intensities: np.ndarray) -> np.ndarray:
        raise NotImplementedError("子类必须实现此方法")


class AkMultiCurveSolver(AbstractAkSolver):
    def __init__(self, spread_specification: np.ndarray):
        super().__init__(spread_specification)
        n_estimates = len(spread_specification) * (len(spread_specification) - 1) // 2
        self.k_estimates = np.zeros(n_estimates)
        self.a_estimates = np.zeros(n_estimates)

    def solve_ak(self, intensities: np.ndarray) -> np.ndarray:
        est_idx = 0
        with np.errstate(divide='ignore', invalid='ignore'):
            for i in range(len(intensities) - 1):
                for j in range(i + 1, len(intensities)):
                    self.k_estimates[est_idx] = (
                        np.log(intensities[j] / intensities[i]) /
                        (self.spread_specification[i] - self.spread_specification[j])
                    )
                    self.a_estimates[est_idx] = (
                        intensities[i] * np.exp(self.k_estimates[est_idx] * self.spread_specification[i])
                    )
                    est_idx += 1

        return np.array([np.mean(self.a_estimates), np.mean(self.k_estimates)])


class SpreadIntensityCurve:
    def __init__(self, spread_step: float, n_spreads: int, dt: int):
        self.intensity_estimators: List[EmpiricalIntensityEstimator] = []
        spread_specification = np.zeros(n_spreads)
        self.intensity_estimates = np.zeros(n_spreads)

        for i in range(n_spreads):
            spread_specification[i] = i * spread_step
            self.intensity_estimators.append(
                EmpiricalIntensityEstimator(spread_specification[i], np.sign(spread_step), dt)
            )
        self.solver = AkMultiCurveSolver(spread_specification)

    def on_tick(self, ref_price: float, fill_price: float, ts: int, window_start: int):
        for estimator in self.intensity_estimators:
            estimator.on_tick(ref_price, fill_price, ts, window_start)

    def estimate_ak(self, ts: int, window_start: int) -> np.ndarray:
        for i, estimator in enumerate(self.intensity_estimators):
            self.intensity_estimates[i] = estimator.estimate_intensity(ts, window_start)
        return self.solver.solve_ak(self.intensity_estimates)


class PendingOrder:
    """待处理委托"""
    def __init__(self, order_id: str, action: str, price: float, quantity: int, 
                 submit_time: int, execute_time: int, reason: str):
        self.order_id = order_id
        self.action = action
        self.price = price
        self.quantity = quantity
        self.submit_time = submit_time
        self.execute_time = execute_time
        self.reason = reason
        self.status = 'pending'  # pending, filled, cancelled


class BacktestConfig:
    """回测配置参数（保持向后兼容性，新增延时参数）"""
    def __init__(self):
        # AS模型参数
        self.dt = 2600  # AS模型时间间隔(毫秒)
        self.intensity_window = 408  # AS模型评估窗口(秒)
        self.intensity_nspread = 2  # 强度估算价差层数
        self.price_step = 0.001  # 最小价格差
        
        # 交易参数
        self.enter_lot = 630  # 基础下单量
        self.max_position = 14924  # 最大持仓
        self.tp_spread = 0.003  # 止盈价差
        self.grid_spread = 0.005  # 网格价差
        self.fee_rate = 0.0001  # 手续费率
        
        # 策略参数
        self.order_update_interval = 15000  # 挂单价格更新间隔(毫秒)
        self.fallback_timeout = 12000  # spread回退超时时间(毫秒)
        
        # === 新增：延时参数 ===
        self.enable_latency = True       # 是否启用延时模拟
        self.network_latency_mean = 100  # 网络延时均值(ms)
        self.network_latency_std = 30    # 网络延时标准差(ms)
        self.system_latency = 20         # 系统处理延时(ms)
        self.exchange_latency = 5        # 交易所延时(ms)
        self.slippage_rate = 0.0001      # 滑点率
        self.market_impact_factor = 0.00001  # 市场冲击因子
        
        # === 订单管理 ===
        self.order_timeout = 30000       # 订单超时时间(ms)
        self.max_pending_orders = 10     # 最大待处理订单数
        
        # 初始资金
        self.initial_cash = 1000000  # 100万初始资金


class BacktestEngine:
    """回测引擎（支持延时模拟，保持向后兼容性）"""
    
    def __init__(self, config: BacktestConfig):
        self.config = config
        self.reset()
        
    def reset(self):
        """重置回测状态"""
        # 资金状态
        self.cash = self.config.initial_cash
        self.position = 0
        self.total_value = self.config.initial_cash
        
        # 交易状态
        self.cost_price = 0.0
        self.buy_price = 0.0
        self.sell_price = 0.0
        self.exit_price = 0.0
        self.sell_enable = False
        self.if_add_vol = False
        self.reach_tp1 = False
        
        # AS模型状态
        self.buy_est = SpreadIntensityCurve(
            spread_step=-self.config.price_step, 
            n_spreads=self.config.intensity_nspread, 
            dt=self.config.dt
        )
        self.sell_est = SpreadIntensityCurve(
            spread_step=self.config.price_step, 
            n_spreads=self.config.intensity_nspread, 
            dt=self.config.dt
        )
        
        # 价差状态
        self.last_bid_spread = 0.004
        self.last_ask_spread = 0.004
        self.last_valid_time = 0
        self.timestamp_update_price = 0
        
        # === 延时相关状态 ===
        self.pending_orders: List[PendingOrder] = []  # 待处理订单队列
        self.order_counter = 0  # 订单计数器
        self.current_tick_data = None  # 当前tick数据
        self.price_history_detailed = deque(maxlen=100)  # 详细价格历史
        
        # 交易记录
        self.trades = []
        self.equity_curve = []
        self.price_history = []
        self.cancelled_orders = []  # 取消的订单
        
    def generate_latency(self) -> int:
        """生成随机网络延时"""
        if not self.config.enable_latency:
            return 0
            
        latency = np.random.normal(self.config.network_latency_mean, self.config.network_latency_std)
        total_latency = max(10, int(latency)) + self.config.system_latency + self.config.exchange_latency
        return total_latency
    
    def calculate_slippage(self, action: str, price: float, quantity: int) -> float:
        """计算滑点"""
        if not self.config.enable_latency:
            return 0.0
            
        # 基础滑点
        base_slippage = self.config.slippage_rate * price
        
        # 市场冲击（基于交易量）
        market_impact = self.config.market_impact_factor * quantity * price
        
        # 价格变动影响（基于最近价格波动）
        price_volatility = 0.0
        if len(self.price_history_detailed) > 1:
            recent_prices = list(self.price_history_detailed)[-10:]
            price_volatility = float(np.std(recent_prices)) if len(recent_prices) > 1 else 0.0
        
        total_slippage = base_slippage + market_impact + price_volatility * 0.1
        
        # 买入时价格上涨，卖出时价格下跌
        if action == 'buy':
            return float(total_slippage)
        else:
            return float(-total_slippage)
    
    def submit_order(self, action: str, price: float, quantity: int, timestamp: int, reason: str) -> str:
        """提交订单（根据配置决定是否立即成交）"""
        if not self.config.enable_latency:
            # 兼容模式：立即执行
            result = self.execute_trade_immediate(action, price, quantity, timestamp, reason)
            return f"IMMEDIATE_{timestamp}_{action}" if result else f"FAILED_{timestamp}_{action}"
        
        # 延时模式：提交待处理订单
        order_id = f"ORDER_{self.order_counter}_{timestamp}"
        self.order_counter += 1
        
        # 计算执行时间（当前时间 + 延时）
        latency = self.generate_latency()
        execute_time = timestamp + latency
        
        # 创建待处理订单
        order = PendingOrder(order_id, action, price, quantity, timestamp, execute_time, reason)
        self.pending_orders.append(order)
        
        # 限制待处理订单数量
        if len(self.pending_orders) > self.config.max_pending_orders:
            # 取消最老的订单
            old_order = self.pending_orders.pop(0)
            old_order.status = 'cancelled'
            self.cancelled_orders.append(old_order)
        
        return order_id
    
    def process_pending_orders(self, timestamp: int):
        """处理到期的待处理订单"""
        if not self.config.enable_latency:
            return
            
        current_tick = self.current_tick_data
        if current_tick is None:
            return
        
        executed_orders = []
        
        for order in self.pending_orders[:]:
            # 检查是否到期执行
            if timestamp >= order.execute_time:
                # 检查订单是否超时
                if timestamp - order.submit_time > self.config.order_timeout:
                    order.status = 'cancelled'
                    self.cancelled_orders.append(order)
                    self.pending_orders.remove(order)
                    continue
                
                # 尝试执行订单
                if self.execute_order_with_slippage(order, current_tick, timestamp):
                    executed_orders.append(order)
                    self.pending_orders.remove(order)
    
    def execute_order_with_slippage(self, order: PendingOrder, current_tick: Dict, timestamp: int) -> bool:
        """考虑滑点执行订单"""
        # 获取当前市场价格
        bid_price = float(current_tick['bid_price'])
        ask_price = float(current_tick['ask_price'])
        mid_price = (bid_price + ask_price) / 2
        
        # 检查价格是否还有效（简单的成交条件检查）
        if order.action == 'buy':
            # 买单：当前价格低于等于委托价格时成交
            if mid_price <= order.price:
                execution_price = min(order.price, ask_price)  # 不能低于当前卖价
            else:
                return False  # 价格不满足，不成交
        else:
            # 卖单：当前价格高于等于委托价格时成交
            if mid_price >= order.price:
                execution_price = max(order.price, bid_price)  # 不能高于当前买价
            else:
                return False  # 价格不满足，不成交
        
        # 计算滑点
        slippage = self.calculate_slippage(order.action, execution_price, order.quantity)
        final_price = execution_price + slippage
        
        # 确保价格合理
        final_price = max(0.001, final_price)
        
        # 执行交易
        return self.execute_trade_immediate(order.action, final_price, order.quantity, timestamp, order.reason, order_id=order.order_id)
    
    def execute_trade_immediate(self, action: str, price: float, quantity: int, timestamp: int, reason: str, order_id: Optional[str] = None) -> bool:
        """立即执行交易"""
        if action == 'buy':
            cost = price * quantity * (1 + self.config.fee_rate)
            if self.cash >= cost:
                self.cash -= cost
                old_position = self.position
                self.position += quantity
                
                # 更新成本价（加权平均）
                if old_position > 0:
                    total_cost = self.cost_price * old_position + price * quantity
                    self.cost_price = total_cost / self.position
                else:
                    self.cost_price = price
                    
                self.sell_enable = True
                
                trade_info = {
                    'timestamp': timestamp,
                    'action': action,
                    'price': price,
                    'quantity': quantity,
                    'cost': cost,
                    'position': self.position,
                    'cash': self.cash,
                    'reason': reason,
                    'order_id': order_id
                }
                self.trades.append(trade_info)
                return True
        
        elif action == 'sell':
            if self.position >= quantity:
                revenue = price * quantity * (1 - self.config.fee_rate)
                self.cash += revenue
                self.position -= quantity
                
                if self.position == 0:
                    self.sell_enable = False
                    self.cost_price = 0.0
                    self.if_add_vol = False
                    self.reach_tp1 = False
                
                trade_info = {
                    'timestamp': timestamp,
                    'action': action,
                    'price': price,
                    'quantity': quantity,
                    'revenue': revenue,
                    'position': self.position,
                    'cash': self.cash,
                    'reason': reason,
                    'order_id': order_id
                }
                self.trades.append(trade_info)
                return True
        
        return False
    
    # 保持向后兼容性
    def execute_trade(self, action: str, price: float, quantity: int, timestamp: int, reason: str):
        """执行交易（向后兼容方法）"""
        return self.submit_order(action, price, quantity, timestamp, reason)
    
    def dynamic_mid(self, ask: float, bid: float, ask_qty: float = 0.001, bid_qty: float = 0.001) -> float:
        """动态中间价计算"""
        imbalance = max(ask_qty, bid_qty) / min(ask_qty, bid_qty)
        if imbalance > 3.0:
            return (ask * bid_qty + bid * ask_qty) / (bid_qty + ask_qty)
        else:
            return (ask + bid) / 2
    
    def get_intensity(self, target_spread: float, a: float, k: float) -> float:
        """根据AS模型参数计算强度"""
        return a * math.exp(-k * target_spread)
    
    def get_spread(self, target_intensity: float, a: float, k: float) -> float:
        """根据强度计算价差"""
        return -(math.log(target_intensity / a)) / k
    
    def calculate_spread(self, mid_price: float, ask_price: float, bid_price: float, unix_time: int):
        """计算AS模型价差"""
        # 更新AS模型
        self.sell_est.on_tick(mid_price, ask_price, unix_time, unix_time - self.config.intensity_window * 1000)
        self.buy_est.on_tick(mid_price, bid_price, unix_time, unix_time - self.config.intensity_window * 1000)

        buy_ak = self.buy_est.estimate_ak(unix_time, unix_time - self.config.intensity_window * 1000)
        sell_ak = self.sell_est.estimate_ak(unix_time, unix_time - self.config.intensity_window * 1000)

        bid_spread = 0.0
        ask_spread = 0.0
        
        if not np.any(np.isnan(buy_ak)):
            if buy_ak[0] * buy_ak[1] != 0:
                filtered_bid_intensity = self.buy_est.intensity_estimates[self.buy_est.intensity_estimates > 1e-6]
                if len(filtered_bid_intensity) and filtered_bid_intensity[-1] * buy_ak[0] > 0:
                    if len(filtered_bid_intensity) < 4:
                        if filtered_bid_intensity[-1] > 8e-3:
                            bid_spread = self.get_spread(filtered_bid_intensity[-1], buy_ak[0], buy_ak[1]) + 0.001
                        else:
                            bid_spread = self.get_spread(filtered_bid_intensity[-1], buy_ak[0], buy_ak[1])
                    else:
                        bid_spread = self.get_spread(filtered_bid_intensity[2], buy_ak[0], buy_ak[1])
                        
            if sell_ak[0] * sell_ak[1] != 0:
                filtered_ask_intensity = self.sell_est.intensity_estimates[self.sell_est.intensity_estimates > 1e-6]
                if len(filtered_ask_intensity) and filtered_ask_intensity[-1] * sell_ak[0] > 0:
                    if len(filtered_ask_intensity) < 4:
                        ask_spread = self.get_spread(filtered_ask_intensity[-1], sell_ak[0], sell_ak[1]) + 0.001
                    else:
                        ask_spread = self.get_spread(filtered_ask_intensity[3], sell_ak[0], sell_ak[1])
        
        # 回退机制
        if unix_time - self.last_valid_time > self.config.fallback_timeout and bid_spread == 0:
            bid_spread = 0.003
            ask_spread = 0.003
        elif unix_time - self.last_valid_time <= self.config.fallback_timeout and bid_spread == 0:
            bid_spread = self.last_bid_spread
            ask_spread = self.last_ask_spread
        else:
            self.last_valid_time = unix_time
            
        self.last_bid_spread = bid_spread
        self.last_ask_spread = ask_spread
        
        return bid_spread, ask_spread
    
    def on_tick(self, row):
        """处理每个tick数据"""
        timestamp = int(row['timestamp'])
        
        # 只处理有完整价格数据的行情
        if pd.isna(row['bid_price']) or pd.isna(row['ask_price']) or pd.isna(row['last_price']):
            return
            
        last_price = float(row['last_price'])
        bid_price = float(row['bid_price'])
        ask_price = float(row['ask_price'])
        bid_vol = float(row['bid_vol']) if not pd.isna(row['bid_vol']) else 0.001
        ask_vol = float(row['ask_vol']) if not pd.isna(row['ask_vol']) else 0.001
        
        # 验证价格数据有效性
        if ask_price <= 0 or bid_price <= 0 or ask_price <= bid_price:
            return
            
        mid_price = self.dynamic_mid(ask_price, bid_price, ask_vol, bid_vol)
        
        # 保存当前tick数据供延时执行使用
        if self.config.enable_latency:
            self.current_tick_data = row.to_dict()
            self.price_history_detailed.append(mid_price)
            
            # 首先处理待处理订单
            self.process_pending_orders(timestamp)
        
        # 计算AS模型价差
        bid_spread, ask_spread = self.calculate_spread(mid_price, ask_price, bid_price, timestamp)
        
        # 更新挂单价格
        if timestamp - self.timestamp_update_price > self.config.order_update_interval:
            self.buy_price = math.floor((last_price - bid_spread) * 1000) / 1000
            self.sell_price = math.floor((last_price + ask_spread) * 1000) / 1000
            self.timestamp_update_price = timestamp
        
        # 交易逻辑
        if not self.sell_enable:  # 无持仓
            if mid_price <= self.buy_price:
                if self.position + self.config.enter_lot <= self.config.max_position:
                    self.submit_order('buy', self.buy_price, self.config.enter_lot, timestamp, 'initial_entry')
        else:  # 有持仓
            self.exit_price = self.cost_price + self.config.tp_spread
            
            if self.if_add_vol:  # 已加仓状态
                if mid_price < self.cost_price - self.config.grid_spread - bid_spread:
                    if self.position + self.config.enter_lot <= self.config.max_position:
                        self.submit_order('buy', mid_price, self.config.enter_lot, timestamp, 'grid_add_continued')
                        self.reach_tp1 = False
                        
                elif mid_price > self.exit_price and not self.reach_tp1:
                    tp1_quantity = max(100, self.position // 2)
                    self.submit_order('sell', mid_price, tp1_quantity, timestamp, 'take_profit_1')
                    self.reach_tp1 = True
                    
                elif mid_price > self.exit_price + self.config.tp_spread and self.reach_tp1:
                    self.submit_order('sell', mid_price, self.position, timestamp, 'take_profit_2_full')
                    
            else:  # 未加仓状态
                if mid_price > self.exit_price:
                    self.submit_order('sell', mid_price, self.position, timestamp, 'simple_profit')
                    
                elif mid_price < self.cost_price - self.config.grid_spread:
                    if self.position + self.config.enter_lot <= self.config.max_position:
                        self.submit_order('buy', mid_price, self.config.enter_lot, timestamp, 'grid_add_first')
                        self.if_add_vol = True
        
        # 记录净值曲线
        self.total_value = self.cash + self.position * mid_price
        equity_entry = {
            'timestamp': timestamp,
            'total_value': self.total_value,
            'cash': self.cash,
            'position': self.position,
            'price': mid_price
        }
        
        if self.config.enable_latency:
            equity_entry['pending_orders'] = len(self.pending_orders)
            
        self.equity_curve.append(equity_entry)
        self.price_history.append(mid_price)
    
    def run_backtest(self, data_file: str):
        """运行回测"""
        mode_desc = "真实回测（考虑延时）" if self.config.enable_latency else "理想回测"
        print(f"开始{mode_desc}: {data_file}")
        
        # 读取数据
        df = pd.read_csv(data_file)
        print(f"数据量: {len(df)} 条")
        
        # 重置状态
        self.reset()
        
        # 逐行处理数据
        for index, row in df.iterrows():
            self.on_tick(row)
            
            if int(index) % 10000 == 0:
                if self.config.enable_latency:
                    print(f"处理进度: {int(index)}/{len(df)} ({int(index)/len(df)*100:.1f}%), "
                          f"待处理订单: {len(self.pending_orders)}")
                else:
                    print(f"处理进度: {int(index)}/{len(df)} ({int(index)/len(df)*100:.1f}%)")
        
        # 处理剩余的待处理订单
        if self.config.enable_latency:
            final_timestamp = int(df.iloc[-1]['timestamp']) + 60000  # 额外等待1分钟
            self.process_pending_orders(final_timestamp)
        
        print(f"{mode_desc}完成")
        return self.get_results()
    
    def get_results(self):
        """获取回测结果"""
        if not self.equity_curve:
            return None
            
        initial_value = self.config.initial_cash
        final_value = self.equity_curve[-1]['total_value']
        total_return = (final_value - initial_value) / initial_value
        
        # 计算最大回撤
        equity_values = [point['total_value'] for point in self.equity_curve]
        peak = equity_values[0]
        max_drawdown = 0
        
        for value in equity_values:
            if value > peak:
                peak = value
            drawdown = (peak - value) / peak
            if drawdown > max_drawdown:
                max_drawdown = drawdown
        
        results = {
            'initial_value': initial_value,
            'final_value': final_value,
            'total_return': total_return,
            'max_drawdown': max_drawdown,
            'total_trades': len(self.trades),
            'final_position': self.position,
            'final_cash': self.cash,
            'trades': self.trades,
            'equity_curve': self.equity_curve
        }
        
        if self.config.enable_latency:
            results['cancelled_orders'] = len(self.cancelled_orders)
            results['pending_orders'] = len(self.pending_orders)
        
        return results
    
    def print_results(self, results):
        """打印回测结果"""
        if results is None:
            print("无回测结果")
            return
            
        mode_desc = "回测结果（考虑延时）" if self.config.enable_latency else "回测结果（理想模式）"
        print(f"\n=== {mode_desc} ===")
        print(f"初始资金: {results['initial_value']:,.2f}")
        print(f"最终净值: {results['final_value']:,.2f}")
        print(f"总收益率: {results['total_return']*100:.2f}%")
        print(f"最大回撤: {results['max_drawdown']*100:.2f}%")
        print(f"交易次数: {results['total_trades']}")
        print(f"最终持仓: {results['final_position']}")
        print(f"最终现金: {results['final_cash']:,.2f}")
        
        if self.config.enable_latency:
            print(f"取消订单数: {results.get('cancelled_orders', 0)}")
            print(f"未完成订单: {results.get('pending_orders', 0)}")
        
        if results['trades']:
            print("\n=== 最近5笔交易 ===")
            for trade in results['trades'][-5:]:
                timestamp = datetime.datetime.fromtimestamp(trade['timestamp']/1000)
                order_id = trade.get('order_id', '')
                order_info = f" | {order_id}" if order_id else ""
                print(f"{timestamp} | {trade['action'].upper()} {trade['quantity']}@{trade['price']:.3f} | {trade['reason']}{order_info}")


def main():
    """主函数"""
    # 创建配置（默认启用延时）
    config = BacktestConfig()
    
    # 创建回测引擎
    engine = BacktestEngine(config)
    
    # 查找数据文件
    data_dir = "backtest_data/merged"
    data_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]
    
    if not data_files:
        print("未找到数据文件")
        return
    
    # 运行回测（使用第一个文件）
    data_file = os.path.join(data_dir, data_files[0])
    results = engine.run_backtest(data_file)
    
    # 打印结果
    engine.print_results(results)
    
    if config.enable_latency:
        print(f"\n=== 延时参数配置 ===")
        print(f"网络延时: {config.network_latency_mean}±{config.network_latency_std}ms")
        print(f"系统延时: {config.system_latency}ms")
        print(f"交易所延时: {config.exchange_latency}ms")
        print(f"滑点率: {config.slippage_rate*100:.4f}%")
        print(f"市场冲击因子: {config.market_impact_factor}")
    
    return results


if __name__ == "__main__":
    results = main()
