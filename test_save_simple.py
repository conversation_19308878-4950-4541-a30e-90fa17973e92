#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试保存功能
"""

import os

def test_save():
    """测试保存功能"""
    try:
        from qmt_optuna_backtest import save_optimization_results
        print('✅ 保存函数导入成功')
        
        # 创建最简单的测试数据
        test_results = {
            'best_params': {'dt': 2000},
            'best_value': 0.15,
            'train_results': {'total_return': 0.12},
            'test_results': {'total_return': 0.10},
            'generalization_ratio': 0.83
        }
        
        print('📊 测试数据创建成功')
        
        # 测试保存（不包含图表）
        print('💾 开始保存测试...')
        save_dir = save_optimization_results(
            test_results,
            save_plots=False,  # 跳过图表
            save_config=False  # 跳过配置文件
        )
        
        print(f'✅ 保存完成: {save_dir}')
        
        # 检查文件
        if os.path.exists(save_dir):
            files = os.listdir(save_dir)
            print(f'📂 生成文件: {files}')
            return True
        else:
            print(f'❌ 目录不存在: {save_dir}')
            return False
            
    except Exception as e:
        print(f'❌ 错误: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 简单保存测试...")
    
    if test_save():
        print("\n✅ 保存功能正常!")
        print("如果运行优化时没有保存，可能是:")
        print("1. 优化过程中出现了错误")
        print("2. 没有看到保存相关的输出信息")
        print("3. 保存目录权限问题")
    else:
        print("\n❌ 保存功能有问题")
