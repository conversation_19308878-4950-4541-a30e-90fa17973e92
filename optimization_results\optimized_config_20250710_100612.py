"""
Optuna优化后的最佳参数配置 - 自动生成
生成时间: 2025-07-10 10:06:17
优化目标值: -0.161786
训练集收益率: -0.0045
测试集收益率: -0.0064
泛化性能: 1.42
"""

from qmt_optuna_backtest import BacktestConfig

def get_optimized_config() -> BacktestConfig:
    """获取自动优化的最佳参数配置"""
    return BacktestConfig(
        # 基础参数
        initial_cash=1000000.0,
        max_position=72642,
        tick_size=0.001,
        commission_rate=0.0001,

        # 策略核心参数 (自动优化)
        dt=1948,
        window_size=291993,
        order_update_interval=16812,

        # 网格交易参数
        grid_levels=2,
        base_quantity=1086,
        max_spread=0.0012655241991607375,
        min_spread=0.0014097292251552774,

        # 原策略参数
        ema_span=0.009148840749271725,
        ema_spread=-0.0032748507111994406,
        intensity_nspread=6,
        tp_spread=0.009319697159783449,

        # 新增参数
        risk_probility=0.044700805753018255,
        risk_probility_buy=0.00500260134086505,
        risk_probility_sell=0.028538128491939575,
        enter_lot=5000,
        grid_spread=0.004425931670456334,
        sl_ratio=4.181341589696876,

        # 止盈参数 - 简化版本
        take_profit_levels=[
            (0.009319697159783449, 1.0)  # 使用tp_spread值，全平
        ]
    )

def get_performance_summary():
    """获取优化结果的业绩摘要"""
    return {
        'optimization_timestamp': '20250710_100612',
        'best_objective_value': -0.161786,
        'train_performance': {
            'total_return': -0.0045,
            'sharpe_ratio': -0.0025,
            'max_drawdown': 0.0278,
            'win_rate': 0.2810,
            'profit_factor': 1.1862,
            'total_trades': 153
        },
        'test_performance': {
            'total_return': -0.0064,
            'sharpe_ratio': -0.1543,
            'max_drawdown': 0.0186,
            'win_rate': 0.2381,
            'profit_factor': 0.6266,
            'total_trades': 42
        },
        'generalization_ratio': 1.4230
    }
