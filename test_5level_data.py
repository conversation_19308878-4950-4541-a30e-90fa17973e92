#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试5档数据保留
"""

import pandas as pd
import os

def test_original_snapshot_data():
    """测试原始snapshot数据是否包含5档信息"""
    try:
        snapshot_file = "backtest_data/snapshot/sh513120_20250613.csv"
        
        if not os.path.exists(snapshot_file):
            print(f"❌ 文件不存在: {snapshot_file}")
            return False
        
        print(f"📊 检查原始snapshot数据: {snapshot_file}")
        
        # 尝试不同编码读取
        for encoding in ['gbk', 'utf-8-sig', 'utf-8', 'latin1']:
            try:
                df = pd.read_csv(snapshot_file, encoding=encoding, nrows=5)
                print(f"✅ 使用编码 {encoding} 成功读取")
                break
            except UnicodeDecodeError:
                continue
        else:
            print("❌ 无法读取文件，所有编码都失败")
            return False
        
        print(f"📋 列名:")
        for i, col in enumerate(df.columns):
            print(f"  {i+1:2d}. {col}")
        
        # 检查是否有5档数据
        level_cols = []
        for i in range(1, 6):
            bid_col = f"申买价{i}"
            ask_col = f"申卖价{i}"
            bid_vol_col = f"申买量{i}"
            ask_vol_col = f"申卖量{i}"
            
            if bid_col in df.columns:
                level_cols.append(bid_col)
            if ask_col in df.columns:
                level_cols.append(ask_col)
            if bid_vol_col in df.columns:
                level_cols.append(bid_vol_col)
            if ask_vol_col in df.columns:
                level_cols.append(ask_vol_col)
        
        print(f"\n🎯 找到的5档相关列 ({len(level_cols)}个):")
        for col in level_cols:
            print(f"  - {col}")
        
        if len(level_cols) >= 16:  # 至少应该有4*4=16个列（价格+数量，买卖各5档）
            print("✅ 原始数据包含完整的5档信息")
            return True
        else:
            print("❌ 原始数据缺少5档信息")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_merged_data_with_5levels():
    """测试合并后的数据是否保留5档信息"""
    try:
        from merge_snapshot_trade import merge_snapshot_trade
        
        snapshot_file = "backtest_data/snapshot/sh513120_20250613.csv"
        trade_file = "backtest_data/trade/sh513120_20250613.csv"
        
        if not os.path.exists(snapshot_file) or not os.path.exists(trade_file):
            print("❌ 源文件不存在，跳过合并测试")
            return False
        
        print(f"\n🔄 测试数据合并（保留5档）...")
        
        # 执行合并
        merged_df = merge_snapshot_trade(snapshot_file, trade_file)
        
        print(f"✅ 合并完成，共 {len(merged_df)} 行")
        
        # 检查列名
        print(f"\n📋 合并后的列名:")
        for i, col in enumerate(merged_df.columns):
            print(f"  {i+1:2d}. {col}")
        
        # 检查5档数据列
        level_cols_found = []
        for i in range(1, 6):
            for prefix in ['bid_price_', 'ask_price_', 'bid_vol_', 'ask_vol_']:
                col = f"{prefix}{i}"
                if col in merged_df.columns:
                    level_cols_found.append(col)
        
        print(f"\n🎯 找到的5档列 ({len(level_cols_found)}个):")
        for col in level_cols_found:
            print(f"  - {col}")
        
        # 检查数据内容
        print(f"\n📊 5档数据样本:")
        sample_row = merged_df.iloc[0]
        for i in range(1, 6):
            bid_price = sample_row.get(f'bid_price_{i}', 'N/A')
            ask_price = sample_row.get(f'ask_price_{i}', 'N/A')
            bid_vol = sample_row.get(f'bid_vol_{i}', 'N/A')
            ask_vol = sample_row.get(f'ask_vol_{i}', 'N/A')
            print(f"  第{i}档: 买{bid_price}@{bid_vol} | 卖{ask_price}@{ask_vol}")
        
        if len(level_cols_found) >= 16:
            print("✅ 合并后数据成功保留5档信息")
            return True
        else:
            print("❌ 合并后数据缺少5档信息")
            return False
            
    except Exception as e:
        print(f"❌ 合并测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_compare_old_vs_new():
    """对比旧版本（只有1档）和新版本（有5档）的差异"""
    try:
        old_file = "backtest_data/merged/sh513120_20250613.csv"
        
        if not os.path.exists(old_file):
            print("❌ 旧版本合并文件不存在，跳过对比")
            return False
        
        print(f"\n🔍 对比旧版本和新版本:")
        
        # 读取旧版本
        old_df = pd.read_csv(old_file)
        print(f"旧版本: {len(old_df)} 行, {len(old_df.columns)} 列")
        print(f"旧版本列名: {list(old_df.columns)}")
        
        # 生成新版本
        from merge_snapshot_trade import merge_snapshot_trade
        snapshot_file = "backtest_data/snapshot/sh513120_20250613.csv"
        trade_file = "backtest_data/trade/sh513120_20250613.csv"
        
        new_df = merge_snapshot_trade(snapshot_file, trade_file)
        print(f"新版本: {len(new_df)} 行, {len(new_df.columns)} 列")
        
        # 统计5档列
        level_cols = [col for col in new_df.columns if any(col.startswith(prefix) for prefix in ['bid_price_', 'ask_price_', 'bid_vol_', 'ask_vol_'])]
        print(f"新版本新增5档列: {len(level_cols)} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ 对比测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 测试5档数据保留...")
    print("=" * 60)
    
    # 测试原始数据
    original_ok = test_original_snapshot_data()
    
    if original_ok:
        # 测试合并后数据
        merged_ok = test_merged_data_with_5levels()
        
        if merged_ok:
            # 对比新旧版本
            compare_ok = test_compare_old_vs_new()
            
            if compare_ok:
                print("\n✅ 所有测试通过！")
                print("\n🎉 5档数据保留成功:")
                print("- 原始snapshot数据包含完整5档信息")
                print("- 合并过程正确保留5档数据")
                print("- trade数据正确处理（无5档信息）")
                print("- 向后兼容性保持（bid_price等仍为一档别名）")
                
                print("\n🚀 现在可以重新合并所有数据:")
                print("python merge_snapshot_trade.py --all --data-dir backtest_data --out-dir backtest_data/merged_5levels")
                
            else:
                print("\n❌ 对比测试失败")
        else:
            print("\n❌ 合并测试失败")
    else:
        print("\n❌ 原始数据测试失败")
    
    print("\n📋 主要改进:")
    print("1. snapshot数据保留完整5档买卖价格和数量")
    print("2. trade数据正确处理（5档列设为NaN）")
    print("3. 保持向后兼容性（bid_price = bid_price_1）")
    print("4. 前向填充正确处理5档数据")
    print("5. 聚合函数正确传递5档信息")
