#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试动态价差修复效果
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def test_dynamic_spread():
    """测试动态价差是否真正起作用"""
    try:
        from qmt_optuna_backtest import QMTOptunaBacktest, BacktestConfig
        print("✅ 模块导入成功")
        
        # 创建配置，使用不同的风险偏好值
        config = BacktestConfig(
            initial_cash=1000000.0,
            commission_rate=0.0001,
            max_position=100000,
            dt=1500,
            window_size=300000,
            order_update_interval=15000,
            enter_lot=5000,
            grid_spread=0.008,
            risk_probility=0.01,  # 较小的风险偏好，应该产生较大的价差
            risk_probility_buy=0.01,
            risk_probility_sell=0.015,
            tp_spread=0.004,
            sl_ratio=4.0,
            min_spread=0.001
        )
        
        print(f"✅ 配置创建成功，风险偏好: {config.risk_probility}")
        
        # 创建回测实例
        backtest = QMTOptunaBacktest(config)
        print("✅ 回测实例创建成功")
        
        # 创建测试数据
        print("\n📊 创建测试数据...")
        base_time = datetime.now()
        n_points = 100
        timestamps = [int((base_time + timedelta(seconds=i*10)).timestamp() * 1000) for i in range(n_points)]
        
        # 创建有变化的价格数据
        base_price = 100.0
        price_changes = np.random.normal(0, 0.001, n_points)
        mid_prices = [base_price]
        for change in price_changes[1:]:
            mid_prices.append(mid_prices[-1] + change)
        
        test_data = pd.DataFrame({
            'timestamp': timestamps,
            'bid_price': [p - 0.0005 for p in mid_prices],
            'ask_price': [p + 0.0005 for p in mid_prices],
            'last_price': mid_prices
        })
        
        print(f"✅ 测试数据创建: {len(test_data)} 个点")
        
        # 让AS模型学习数据
        print("\n🧪 AS模型学习阶段...")
        for i in range(min(50, len(test_data))):
            row = test_data.iloc[i]
            timestamp = row['timestamp']
            bid_price = row['bid_price']
            ask_price = row['ask_price']
            last_price = row['last_price']
            
            mid_price = (bid_price + ask_price) / 2
            backtest.update_as_model(mid_price, bid_price, ask_price, timestamp)
        
        print("✅ AS模型学习完成")
        
        # 测试价差计算
        print("\n🎯 测试动态价差计算:")
        spreads_results = []
        
        for i in range(50, min(70, len(test_data))):
            row = test_data.iloc[i]
            timestamp = row['timestamp']
            
            # 模拟挂单更新的条件检查
            should_update = (
                backtest.last_order_update_time == 0 or 
                timestamp - backtest.last_order_update_time >= backtest.config.order_update_interval
            )
            
            if should_update:
                # 获取AS模型计算的价差
                buy_spread, sell_spread = backtest.get_optimal_spreads(timestamp)
                spreads_results.append((buy_spread, sell_spread))
                
                # 更新时间戳
                backtest.last_order_update_time = timestamp
                
                if len(spreads_results) <= 5:  # 只显示前5个结果
                    print(f"  数据点 {i}: buy_spread={buy_spread:.6f}, sell_spread={sell_spread:.6f}")
        
        # 分析价差变化
        if spreads_results:
            buy_spreads = [s[0] for s in spreads_results]
            sell_spreads = [s[1] for s in spreads_results]
            
            buy_spread_std = np.std(buy_spreads)
            sell_spread_std = np.std(sell_spreads)
            buy_spread_mean = np.mean(buy_spreads)
            sell_spread_mean = np.mean(sell_spreads)
            
            print(f"\n📈 价差变化分析:")
            print(f"买入价差: 平均={buy_spread_mean:.6f}, 标准差={buy_spread_std:.6f}")
            print(f"卖出价差: 平均={sell_spread_mean:.6f}, 标准差={sell_spread_std:.6f}")
            print(f"买入价差范围: {min(buy_spreads):.6f} - {max(buy_spreads):.6f}")
            print(f"卖出价差范围: {min(sell_spreads):.6f} - {max(sell_spreads):.6f}")
            
            # 判断是否真正动态
            if buy_spread_std > 0.0001 or sell_spread_std > 0.0001:
                print("✅ 价差确实在动态变化！")
                return True
            elif buy_spread_mean != 0.004:
                print("✅ 价差不是默认值0.004，AS模型在工作！")
                return True
            else:
                print("❌ 价差仍然是固定值，可能还有问题")
                return False
        else:
            print("❌ 没有获得价差计算结果")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 测试动态价差修复效果...")
    print("=" * 50)
    
    if test_dynamic_spread():
        print("\n✅ 动态价差修复成功！")
        print("\n🎉 修复内容:")
        print("1. 修复了首次更新时强制使用默认价差的问题")
        print("2. AS模型现在可以正常计算动态价差")
        print("3. 价差会根据风险偏好和市场数据变化")
        
        print("\n🚀 现在运行优化应该会看到:")
        print("- 价差不再固定为0.004")
        print("- 根据risk_probility参数动态调整")
        print("- AS模型真正发挥作用")
        
    else:
        print("\n❌ 还需要进一步调试")
    
    print("\n💡 提示:")
    print("如果价差仍然固定，可能是AS模型计算出的价差≤0")
    print("导致触发了默认价差逻辑")
