import numpy as np
import datetime
from utils.dollar_bar_ma import QMT_DollarBar_MA_Strategy

class ContextInfo:
    """
    QMT风格的上下文对象（可扩展）
    """
    def __init__(self, tick_data):
        self.tick_data = tick_data  # list of dicts: {'price':..., 'volume':..., 'timestamp':...}
        self.index = 0

    def get_next_tick(self):
        if self.index < len(self.tick_data):
            tick = self.tick_data[self.index]
            self.index += 1
            return tick
        else:
            return None

# 策略主类
class DollarBarMAStrategyQMT:
    def __init__(self, dollar_threshold=1e6, short=5, mid=15, long=30, volume_ratio=2):
        self.signal_detector = QMT_DollarBar_MA_Strategy(
            dollar_threshold=dollar_threshold,
            short=short, mid=mid, long=long, volume_ratio=volume_ratio
        )
        self.log = []

    def on_tick(self, price, volume, timestamp):
        signal = self.signal_detector.on_tick(price, volume, timestamp)
        if signal:
            msg = f"[ALERT] {datetime.datetime.fromtimestamp(timestamp/1000)} 触发强势下跌信号!"
            print(msg)
            self.log.append(msg)

    def run(self, context):
        while True:
            tick = context.get_next_tick()
            if tick is None:
                break
            self.on_tick(tick['price'], tick['volume'], tick['timestamp'])

# 示例：模拟tick数据批量输入
def generate_mock_ticks(num=200, base_price=100, base_vol=1000, dollar_jump=1.5):
    """生成模拟tick数据，部分bar成交额放大，部分价格下跌"""
    ticks = []
    price = base_price
    for i in range(num):
        if i % 50 == 0 and i > 0:
            price -= np.random.uniform(0.5, 1.5)  # 模拟下跌
        volume = base_vol + int(np.random.normal(0, 200))
        if i % 60 == 0 and i > 0:
            volume = int(volume * dollar_jump)  # 模拟成交额放大
        timestamp = int((datetime.datetime.now().timestamp() + i) * 1000)
        ticks.append({'price': price, 'volume': volume, 'timestamp': timestamp})
    return ticks

if __name__ == '__main__':
    # 生成模拟tick数据
    ticks = generate_mock_ticks(num=200)
    context = ContextInfo(ticks)
    # 初始化策略
    strategy = DollarBarMAStrategyQMT(
        dollar_threshold=1e6, short=5, mid=15, long=30, volume_ratio=1.5
    )
    print("=== Dollar Bar 三均线下跌信号策略启动 ===")
    strategy.run(context)
    print("=== 策略运行结束 ===") 