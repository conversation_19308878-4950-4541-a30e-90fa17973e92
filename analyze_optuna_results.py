"""
分析修正后的Optuna优化结果
包含正确的胜率计算
"""

from optimized_config import get_optimized_config
from qmt_optuna_backtest import QMTOptunaBacktest, get_data_files, load_multiple_files, split_train_test_files

def analyze_optuna_results():
    """分析Optuna优化结果的详细表现"""
    print("=== 分析修正后的Optuna优化结果 ===")
    
    # 获取最优配置
    config = get_optimized_config()
    
    # 获取数据文件
    all_files = get_data_files("backtest_data/merged")
    train_files, test_files = split_train_test_files(all_files, 0.7)
    
    print(f"\n=== 训练集分析 ===")
    train_data = load_multiple_files(train_files)
    train_backtest = QMTOptunaBacktest(config)
    train_results = train_backtest.run_backtest(train_data)
    
    # 分析训练集交易
    train_winning = sum(1 for trade in train_backtest.trades if trade.pnl > 0)
    train_losing = sum(1 for trade in train_backtest.trades if trade.pnl < 0)
    train_zero = sum(1 for trade in train_backtest.trades if trade.pnl == 0)
    
    print(f"训练集交易分析:")
    print(f"  总交易数: {len(train_backtest.trades)}")
    print(f"  盈利交易: {train_winning} ({train_winning/len(train_backtest.trades):.1%})")
    print(f"  亏损交易: {train_losing} ({train_losing/len(train_backtest.trades):.1%})")
    print(f"  零盈亏交易: {train_zero} ({train_zero/len(train_backtest.trades):.1%})")
    print(f"  胜率: {train_results['win_rate']:.1%}")
    print(f"  盈亏比: {train_results['profit_factor']:.2f}")
    print(f"  总收益率: {train_results['total_return']:.2%}")
    print(f"  夏普比率: {train_results['sharpe_ratio']:.2f}")
    print(f"  最大回撤: {train_results['max_drawdown']:.2%}")
    
    print(f"\n=== 测试集分析 ===")
    test_data = load_multiple_files(test_files)
    test_backtest = QMTOptunaBacktest(config)
    test_results = test_backtest.run_backtest(test_data)
    
    # 分析测试集交易
    test_winning = sum(1 for trade in test_backtest.trades if trade.pnl > 0)
    test_losing = sum(1 for trade in test_backtest.trades if trade.pnl < 0)
    test_zero = sum(1 for trade in test_backtest.trades if trade.pnl == 0)
    
    print(f"测试集交易分析:")
    print(f"  总交易数: {len(test_backtest.trades)}")
    print(f"  盈利交易: {test_winning} ({test_winning/len(test_backtest.trades):.1%})")
    print(f"  亏损交易: {test_losing} ({test_losing/len(test_backtest.trades):.1%})")
    print(f"  零盈亏交易: {test_zero} ({test_zero/len(test_backtest.trades):.1%})")
    print(f"  胜率: {test_results['win_rate']:.1%}")
    print(f"  盈亏比: {test_results['profit_factor']:.2f}")
    print(f"  总收益率: {test_results['total_return']:.2%}")
    print(f"  夏普比率: {test_results['sharpe_ratio']:.2f}")
    print(f"  最大回撤: {test_results['max_drawdown']:.2%}")
    
    print(f"\n=== 完整数据集分析 ===")
    all_data = load_multiple_files(all_files)
    full_backtest = QMTOptunaBacktest(config)
    full_results = full_backtest.run_backtest(all_data)
    
    # 分析完整数据集交易
    full_winning = sum(1 for trade in full_backtest.trades if trade.pnl > 0)
    full_losing = sum(1 for trade in full_backtest.trades if trade.pnl < 0)
    full_zero = sum(1 for trade in full_backtest.trades if trade.pnl == 0)
    
    print(f"完整数据集交易分析:")
    print(f"  总交易数: {len(full_backtest.trades)}")
    print(f"  盈利交易: {full_winning} ({full_winning/len(full_backtest.trades):.1%})")
    print(f"  亏损交易: {full_losing} ({full_losing/len(full_backtest.trades):.1%})")
    print(f"  零盈亏交易: {full_zero} ({full_zero/len(full_backtest.trades):.1%})")
    print(f"  胜率: {full_results['win_rate']:.1%}")
    print(f"  盈亏比: {full_results['profit_factor']:.2f}")
    print(f"  总收益率: {full_results['total_return']:.2%}")
    print(f"  夏普比率: {full_results['sharpe_ratio']:.2f}")
    print(f"  最大回撤: {full_results['max_drawdown']:.2%}")
    
    # 盈利交易分析
    if full_winning > 0:
        winning_trades = [trade for trade in full_backtest.trades if trade.pnl > 0]
        avg_win = sum(trade.pnl for trade in winning_trades) / len(winning_trades)
        max_win = max(trade.pnl for trade in winning_trades)
        min_win = min(trade.pnl for trade in winning_trades)
        
        print(f"\n=== 盈利交易详情 ===")
        print(f"  平均盈利: {avg_win:.2f}")
        print(f"  最大盈利: {max_win:.2f}")
        print(f"  最小盈利: {min_win:.2f}")
    
    # 亏损交易分析
    if full_losing > 0:
        losing_trades = [trade for trade in full_backtest.trades if trade.pnl < 0]
        avg_loss = sum(trade.pnl for trade in losing_trades) / len(losing_trades)
        max_loss = min(trade.pnl for trade in losing_trades)  # 最大亏损（最负的数）
        min_loss = max(trade.pnl for trade in losing_trades)  # 最小亏损（最接近0的负数）
        
        print(f"\n=== 亏损交易详情 ===")
        print(f"  平均亏损: {avg_loss:.2f}")
        print(f"  最大亏损: {max_loss:.2f}")
        print(f"  最小亏损: {min_loss:.2f}")
    
    print(f"\n=== 做市商策略特征分析 ===")
    print(f"策略类型: 典型的做市商策略")
    print(f"特征1: 低胜率 ({full_results['win_rate']:.1%}) - 大部分交易是小额亏损")
    print(f"特征2: 高盈亏比 ({full_results['profit_factor']:.1f}) - 少数交易大额盈利")
    print(f"特征3: 稳定收益 ({full_results['total_return']:.2%}) - 通过价差获利")
    print(f"特征4: 低回撤 ({full_results['max_drawdown']:.2%}) - 风险控制良好")
    
    return {
        'train_results': train_results,
        'test_results': test_results,
        'full_results': full_results
    }

if __name__ == "__main__":
    analyze_optuna_results() 