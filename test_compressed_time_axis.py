#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试压缩时间轴功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt

def create_trading_time_data():
    """创建模拟的交易时间数据（包含间隔）"""
    base_date = datetime.now().replace(hour=9, minute=30, second=0, microsecond=0)
    
    # 模拟一天的交易时间段
    time_segments = [
        # 上午交易时间 9:30-11:30
        (base_date, base_date + timedelta(hours=2)),
        # 下午交易时间 13:00-15:00  
        (base_date + timedelta(hours=3.5), base_date + timedelta(hours=5.5)),
        # 夜盘时间 21:00-23:00
        (base_date + timedelta(hours=11.5), base_date + timedelta(hours=13.5))
    ]
    
    all_data = []
    base_price = 100.0
    
    for start_time, end_time in time_segments:
        # 每个时间段生成数据点
        duration_minutes = int((end_time - start_time).total_seconds() / 60)
        times = [start_time + timedelta(minutes=i*5) for i in range(duration_minutes//5)]  # 每5分钟一个点
        
        # 生成价格数据
        n_points = len(times)
        price_changes = np.random.normal(0, 0.05, n_points)
        prices = [base_price]
        for change in price_changes[1:]:
            prices.append(prices[-1] + change)
        
        base_price = prices[-1]
        
        # 创建数据段
        segment_data = pd.DataFrame({
            'datetime': times,
            'timestamp': [int(t.timestamp() * 1000) for t in times],
            'last_price': prices[:len(times)]
        })
        
        all_data.append(segment_data)
    
    # 合并所有数据
    data = pd.concat(all_data, ignore_index=True)
    return data

def test_time_compression():
    """测试时间压缩功能"""
    try:
        # 创建测试数据
        data = create_trading_time_data()
        print(f'✅ 创建测试数据: {len(data)} 个数据点')
        
        # 显示时间分布
        print(f'📊 时间范围: {data["datetime"].min()} 到 {data["datetime"].max()}')
        
        # 计算时间间隔
        time_diffs = data['datetime'].diff().dropna()
        print(f'⏱️ 时间间隔统计:')
        print(f'  最小间隔: {time_diffs.min()}')
        print(f'  最大间隔: {time_diffs.max()}')
        print(f'  中位数间隔: {time_diffs.median()}')
        
        # 检测间隔
        max_gap_hours = time_diffs.max().total_seconds() / 3600
        median_gap_minutes = time_diffs.median().total_seconds() / 60
        
        print(f'📈 数据特征:')
        print(f'  最大间隔: {max_gap_hours:.1f} 小时')
        print(f'  中位数间隔: {median_gap_minutes:.1f} 分钟')
        print(f'  数据不连续: {"是" if max_gap_hours > 1 else "否"}')
        
        return True
        
    except Exception as e:
        print(f'❌ 错误: {e}')
        return False

def show_compression_benefits():
    """显示压缩时间轴的优势"""
    print("\n🎨 压缩时间轴优化效果:")
    print("=" * 50)
    
    print("\n📊 优化前的问题:")
    print("❌ 原始时间轴: 9:30 ——————— 11:30 ————————————————— 13:00 ——————— 15:00")
    print("   显示效果:   [数据] [大段空白(1.5小时)] [数据] [大段空白(6小时)] [数据]")
    print("   问题: 大量空白区域，图表不美观，难以看清数据细节")
    
    print("\n✅ 压缩时间轴后:")
    print("✅ 压缩轴: [9:30数据][11:30数据][13:00数据][15:00数据][21:00数据][23:00数据]")
    print("   显示效果: 连续紧凑显示，去除空白间隔")
    print("   优势: 图表紧凑美观，数据细节清晰可见")
    
    print("\n🔧 技术实现:")
    print("1. 🗜️ 数据点映射: 时间戳 → 连续索引")
    print("2. 🏷️ 智能标签: 自动选择关键时间点显示")
    print("3. 📍 交易点定位: 精确映射买卖点到压缩坐标")
    print("4. 📈 收益曲线: 同步压缩，保持一致性")
    
    print("\n💡 适用场景:")
    print("✅ 股票交易时间 (9:30-11:30, 13:00-15:00)")
    print("✅ 期货夜盘 (21:00-02:30)")
    print("✅ 外汇市场 (跨时区交易)")
    print("✅ 任何有时间间隔的金融数据")

if __name__ == "__main__":
    print("🧪 测试压缩时间轴功能...")
    
    if test_time_compression():
        print("\n✅ 压缩时间轴功能测试通过！")
        show_compression_benefits()
        
        print("\n🚀 现在运行优化会生成美观的压缩时间轴图表:")
        print("python run_optuna_optimization.py")
        
        print("\n📈 新图表特点:")
        print("- 去除非交易时间的空白区域")
        print("- 紧凑显示所有交易数据")
        print("- 智能时间标签")
        print("- 交易点精确定位")
        print("- 标题显示'(压缩时间轴)'说明")
    else:
        print("\n❌ 测试失败")
