# encoding: utf-8
"""
测试qmt_backtest.py回测系统 - 支持批量回测
"""

import os
import pandas as pd
from qmt_backtest import BacktestEngine, BacktestConfig
import datetime

def single_file_backtest(data_file, config):
    """单个文件回测"""
    print(f"\n{'='*50}")
    print(f"回测文件: {os.path.basename(data_file)}")
    print(f"{'='*50}")
    
    # 创建回测引擎
    engine = BacktestEngine(config)
    
    # 运行回测
    results = engine.run_backtest(data_file)
    
    if results:
        # 简化输出
        print(f"总收益率: {results['total_return']*100:.2f}%")
        print(f"最大回撤: {results['max_drawdown']*100:.2f}%")
        print(f"交易次数: {results['total_trades']}")
        print(f"最终持仓: {results['final_position']}")
        print(f"最终净值: {results['final_value']:,.2f}")
        
        return {
            'file': os.path.basename(data_file),
            'total_return': results['total_return'],
            'max_drawdown': results['max_drawdown'],
            'total_trades': results['total_trades'],
            'final_position': results['final_position'],
            'final_value': results['final_value'],
            'final_cash': results['final_cash'],
            'trades': results['trades']
        }
    else:
        print("回测失败")
        return None

def batch_backtest():
    """批量回测所有数据文件"""
    print("=== ETF T+0 AS模型批量回测系统 ===")
    
    # 创建配置
    config = BacktestConfig()
    print(f"回测配置:")
    print(f"  初始资金: {config.initial_cash:,}")
    print(f"  最大持仓: {config.max_position}")
    print(f"  基础下单量: {config.enter_lot}")
    print(f"  AS模型dt: {config.dt}ms")
    print(f"  评估窗口: {config.intensity_window}s")
    
    # 查找所有数据文件
    data_dir = "backtest_data/merged"
    if not os.path.exists(data_dir):
        print(f"数据目录不存在: {data_dir}")
        return
    
    data_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]
    data_files.sort()  # 按文件名排序
    
    if not data_files:
        print("未找到数据文件")
        return
    
    print(f"\n找到 {len(data_files)} 个数据文件")
    
    # 批量运行回测
    all_results = []
    successful_backtests = 0
    
    for data_file in data_files:
        file_path = os.path.join(data_dir, data_file)
        result = single_file_backtest(file_path, config)
        
        if result:
            all_results.append(result)
            successful_backtests += 1
    
    # 汇总结果
    print(f"\n{'='*60}")
    print(f"批量回测汇总结果")
    print(f"{'='*60}")
    print(f"成功回测文件数: {successful_backtests}/{len(data_files)}")
    
    if all_results:
        # 计算汇总统计
        total_returns = [r['total_return'] for r in all_results]
        max_drawdowns = [r['max_drawdown'] for r in all_results]
        total_trades = [r['total_trades'] for r in all_results]
        final_values = [r['final_value'] for r in all_results]
        
        print(f"\n=== 收益率统计 ===")
        print(f"平均收益率: {sum(total_returns)/len(total_returns)*100:.2f}%")
        print(f"最高收益率: {max(total_returns)*100:.2f}%")
        print(f"最低收益率: {min(total_returns)*100:.2f}%")
        print(f"正收益文件数: {len([r for r in total_returns if r > 0])}")
        
        print(f"\n=== 风险统计 ===")
        print(f"平均最大回撤: {sum(max_drawdowns)/len(max_drawdowns)*100:.2f}%")
        print(f"最大回撤(最大): {max(max_drawdowns)*100:.2f}%")
        print(f"最大回撤(最小): {min(max_drawdowns)*100:.2f}%")
        
        print(f"\n=== 交易统计 ===")
        print(f"平均交易次数: {sum(total_trades)/len(total_trades):.1f}")
        print(f"最多交易次数: {max(total_trades)}")
        print(f"最少交易次数: {min(total_trades)}")
        print(f"总交易次数: {sum(total_trades)}")
        
        print(f"\n=== 净值统计 ===")
        print(f"平均最终净值: {sum(final_values)/len(final_values):,.2f}")
        print(f"最高最终净值: {max(final_values):,.2f}")
        print(f"最低最终净值: {min(final_values):,.2f}")
        
        # 详细结果表格
        print(f"\n=== 详细结果表格 ===")
        print(f"{'文件名':<25} {'收益率':<10} {'回撤':<10} {'交易次数':<8} {'最终净值':<12}")
        print("-" * 70)
        
        for result in all_results:
            print(f"{result['file']:<25} "
                  f"{result['total_return']*100:>8.2f}% "
                  f"{result['max_drawdown']*100:>8.2f}% "
                  f"{result['total_trades']:>6} "
                  f"{result['final_value']:>10,.0f}")
        
        # 交易原因分析（汇总所有文件）
        analyze_all_trades(all_results)
        
        # 保存结果到CSV
        save_results_to_csv(all_results)
    
    return all_results

def analyze_all_trades(all_results):
    """分析所有文件的交易原因"""
    print(f"\n=== 汇总交易原因分析 ===")
    
    all_reasons = {}
    total_buy_volume = 0
    total_sell_volume = 0
    
    for result in all_results:
        for trade in result['trades']:
            reason = trade['reason']
            action = trade['action']
            quantity = trade['quantity']
            
            if reason not in all_reasons:
                all_reasons[reason] = {'count': 0, 'volume': 0}
            
            all_reasons[reason]['count'] += 1
            all_reasons[reason]['volume'] += quantity
            
            if action == 'buy':
                total_buy_volume += quantity
            else:
                total_sell_volume += quantity
    
    print(f"总买入量: {total_buy_volume:,}手")
    print(f"总卖出量: {total_sell_volume:,}手")
    print(f"净持仓量: {total_buy_volume - total_sell_volume:,}手")
    
    print(f"\n交易原因详细统计:")
    for reason, stats in sorted(all_reasons.items()):
        print(f"  {reason:<20}: {stats['count']:>4}次, {stats['volume']:>8,}手")

def save_results_to_csv(all_results):
    """保存结果到CSV文件"""
    try:
        # 准备数据
        csv_data = []
        for result in all_results:
            csv_data.append({
                '文件名': result['file'],
                '收益率(%)': result['total_return'] * 100,
                '最大回撤(%)': result['max_drawdown'] * 100,
                '交易次数': result['total_trades'],
                '最终持仓': result['final_position'],
                '最终净值': result['final_value'],
                '最终现金': result['final_cash']
            })
        
        # 保存到CSV
        df = pd.DataFrame(csv_data)
        csv_filename = f"batch_backtest_results_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
        print(f"\n结果已保存到: {csv_filename}")
        
    except Exception as e:
        print(f"\n保存CSV文件失败: {e}")

def main():
    """主函数 - 支持单文件和批量回测"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "single":
        # 单文件回测模式
        print("=== 单文件回测模式 ===")
        config = BacktestConfig()
        engine = BacktestEngine(config)
        
        data_dir = "backtest_data/merged"
        data_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]
        if data_files:
            test_file = os.path.join(data_dir, data_files[0])
            results = engine.run_backtest(test_file)
            if results:
                engine.print_results(results)
    else:
        # 批量回测模式（默认）
        batch_backtest()

if __name__ == "__main__":
    main() 