#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Optuna研究管理工具
用于查看、分析和管理现有的优化研究
"""

import os
import sys
from datetime import datetime
from qmt_optuna_backtest import list_existing_studies, load_existing_study, OptimizationConfig, optimize_parameters_with_validation

def show_study_details(study_name: str):
    """显示study的详细信息"""
    try:
        study = load_existing_study(study_name)
        
        print(f"\n📊 Study详细信息: {study_name}")
        print("=" * 60)
        print(f"研究名称: {study.study_name}")
        print(f"试验总数: {len(study.trials)}")
        print(f"最佳值: {study.best_value:.6f}")
        print(f"创建时间: {study_name.split('_')[-2:]}")  # 从名称提取时间
        
        print(f"\n🏆 最佳参数:")
        for key, value in study.best_params.items():
            if isinstance(value, float):
                print(f"  {key}: {value:.6f}")
            else:
                print(f"  {key}: {value}")
        
        print(f"\n📈 试验历史 (最近10个):")
        recent_trials = study.trials[-10:] if len(study.trials) > 10 else study.trials
        for i, trial in enumerate(recent_trials):
            status = "✅" if trial.state.name == "COMPLETE" else "❌"
            value = f"{trial.value:.6f}" if trial.value else "N/A"
            print(f"  Trial {trial.number}: {status} Value: {value}")
        
        return study
        
    except Exception as e:
        print(f"❌ 加载study失败: {e}")
        return None

def list_all_studies():
    """列出所有现有的studies"""
    studies = list_existing_studies()
    
    if not studies:
        print("📭 没有找到现有的优化研究")
        return
    
    print(f"📚 现有的优化研究 ({len(studies)} 个):")
    print("=" * 60)
    
    for i, study_name in enumerate(studies, 1):
        # 从文件名提取时间信息
        parts = study_name.split('_')
        if len(parts) >= 4:
            date_str = parts[-2]
            time_str = parts[-1]
            try:
                date_obj = datetime.strptime(f"{date_str}_{time_str}", "%Y%m%d_%H%M%S")
                time_display = date_obj.strftime("%Y-%m-%d %H:%M:%S")
            except:
                time_display = f"{date_str}_{time_str}"
        else:
            time_display = "未知时间"
        
        print(f"  {i:2d}. {study_name}")
        print(f"      创建时间: {time_display}")
        
        # 尝试获取基本信息
        try:
            study = load_existing_study(study_name)
            print(f"      试验数: {len(study.trials)}, 最佳值: {study.best_value:.6f}")
        except:
            print(f"      状态: 无法加载")
        print()

def continue_study_interactive():
    """交互式继续优化"""
    studies = list_existing_studies()
    
    if not studies:
        print("📭 没有找到现有的优化研究")
        return
    
    print("🔄 选择要继续的研究:")
    for i, study_name in enumerate(studies[:10], 1):  # 只显示最近10个
        print(f"  {i}. {study_name}")
    
    try:
        choice = int(input("\n请输入序号 (1-10): ")) - 1
        if 0 <= choice < len(studies) and choice < 10:
            selected_study = studies[choice]
            
            # 显示study详情
            study = show_study_details(selected_study)
            if not study:
                return
            
            # 询问是否继续
            additional_trials = input(f"\n继续优化多少次试验? (默认20): ").strip()
            additional_trials = int(additional_trials) if additional_trials else 20
            
            print(f"\n🚀 开始继续优化...")
            
            # 创建配置
            opt_config = OptimizationConfig(n_trials=additional_trials)
            
            # 继续优化
            results = optimize_parameters_with_validation(
                data_dir="backtest_data/merged",
                opt_config=opt_config,
                continue_study=selected_study
            )
            
            print(f"\n✅ 优化完成!")
            print(f"新的最佳值: {results['best_value']:.6f}")
            
        else:
            print("❌ 无效的选择")
            
    except (ValueError, KeyboardInterrupt):
        print("\n❌ 操作取消")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("🔧 Optuna研究管理工具")
        print("\n使用方法:")
        print("  python manage_optuna_studies.py list              # 列出所有studies")
        print("  python manage_optuna_studies.py show <study_name> # 显示study详情")
        print("  python manage_optuna_studies.py continue          # 交互式继续优化")
        return
    
    command = sys.argv[1].lower()
    
    if command == "list":
        list_all_studies()
    
    elif command == "show":
        if len(sys.argv) < 3:
            print("❌ 请指定study名称")
            return
        study_name = sys.argv[2]
        show_study_details(study_name)
    
    elif command == "continue":
        continue_study_interactive()
    
    else:
        print(f"❌ 未知命令: {command}")

if __name__ == "__main__":
    main()
