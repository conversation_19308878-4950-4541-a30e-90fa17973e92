#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Optuna存档功能
"""

def test_optuna_archive():
    """测试Optuna存档功能"""
    try:
        from qmt_optuna_backtest import list_existing_studies
        print('✅ Optuna存档功能导入成功')
        
        # 测试列出studies
        studies = list_existing_studies()
        print(f'📚 当前studies数量: {len(studies)}')
        
        if studies:
            print('现有studies:')
            for study in studies[:3]:  # 只显示前3个
                print(f'  - {study}')
        
        print('\n📊 Optuna存档功能说明:')
        print('1. 🗄️ 自动存档: 每次优化都会创建SQLite数据库')
        print('2. 📈 历史记录: 保存所有试验的参数和结果')
        print('3. 🔄 断点续传: 可以从上次优化的地方继续')
        print('4. 📊 分析工具: 可以分析参数重要性和优化过程')
        
        print('\n🔧 使用方法:')
        print('- 查看所有studies: python manage_optuna_studies.py list')
        print('- 查看study详情: python manage_optuna_studies.py show <study_name>')
        print('- 继续优化: python manage_optuna_studies.py continue')
        
        return True
        
    except Exception as e:
        print(f'❌ 错误: {e}')
        return False

if __name__ == "__main__":
    print("🧪 测试Optuna存档功能...")
    
    if test_optuna_archive():
        print("\n✅ 所有测试通过！")
        print("\n💡 提示:")
        print("- 运行 run_optuna_optimization.py 会自动创建存档")
        print("- 存档位置: optuna_studies/ 目录")
        print("- 每个study都有独立的SQLite数据库文件")
    else:
        print("\n❌ 测试失败")
