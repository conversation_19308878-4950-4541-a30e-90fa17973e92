#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试保存功能修复
"""

def test_function_structure():
    """测试函数结构是否正确"""
    try:
        from qmt_optuna_backtest import optimize_parameters_with_validation
        print('✅ 函数导入成功')
        
        # 检查函数是否能正常调用（不实际运行）
        import inspect
        sig = inspect.signature(optimize_parameters_with_validation)
        print(f'✅ 函数签名: {sig}')
        
        return True
        
    except Exception as e:
        print(f'❌ 错误: {e}')
        return False

if __name__ == "__main__":
    print("🔧 测试保存功能修复...")
    
    if test_function_structure():
        print("\n✅ 修复成功！")
        print("\n🚀 现在可以运行:")
        print("python run_optuna_optimization.py")
        print("\n📊 您应该会看到:")
        print("============================================================")
        print("📁 开始保存优化结果...")
        print("📊 最佳参数数量: X")
        print("📈 最佳目标值: X.XXXXXX")
        print("🔢 完成试验数: X")
        print("💾 调用保存函数...")
        print("✅ 优化结果已保存到: optimization_results")
        print("📂 生成文件数量: X")
        print("============================================================")
    else:
        print("\n❌ 还有问题需要修复")
