#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AS模型是否真正起作用
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def test_as_model_functionality():
    """测试AS模型功能"""
    try:
        from qmt_optuna_backtest import QMTOptunaBacktest, BacktestConfig
        print("✅ 模块导入成功")
        
        # 创建配置
        config = BacktestConfig(
            initial_cash=1000000.0,
            commission_rate=0.0001,
            max_position=100000,
            dt=2000,
            window_size=300000,  # 5分钟窗口
            order_update_interval=15000,
            enter_lot=5000,
            grid_spread=0.008,
            risk_probility=0.01,
            risk_probility_buy=0.01,
            risk_probility_sell=0.015,
            tp_spread=0.004,
            sl_ratio=4.0,
            min_spread=0.001
        )
        
        print("✅ 配置创建成功")
        
        # 创建回测实例
        backtest = QMTOptunaBacktest(config)
        print("✅ 回测实例创建成功")
        
        # 检查AS模型组件是否被初始化
        print("\n🔍 检查AS模型组件:")
        
        # 检查AS_API_AVAILABLE
        from qmt_optuna_backtest import AS_API_AVAILABLE
        print(f"AS_API_AVAILABLE: {AS_API_AVAILABLE}")
        
        if AS_API_AVAILABLE:
            # 检查estimator是否存在
            has_buy_estimator = hasattr(backtest, 'buy_estimator')
            has_sell_estimator = hasattr(backtest, 'sell_estimator')
            has_buy_curve = hasattr(backtest, 'buy_curve')
            has_sell_curve = hasattr(backtest, 'sell_curve')
            
            print(f"buy_estimator: {'✅' if has_buy_estimator else '❌'}")
            print(f"sell_estimator: {'✅' if has_sell_estimator else '❌'}")
            print(f"buy_curve: {'✅' if has_buy_curve else '❌'}")
            print(f"sell_curve: {'✅' if has_sell_curve else '❌'}")
            
            if has_buy_estimator and has_sell_estimator:
                print("✅ AS模型estimator已正确初始化")
            else:
                print("❌ AS模型estimator未正确初始化")
                return False
        else:
            print("⚠ AS模型不可用，将使用固定价差")
        
        # 创建测试数据
        print("\n📊 创建测试数据...")
        base_time = datetime.now()
        n_points = 100
        timestamps = [int((base_time + timedelta(seconds=i*10)).timestamp() * 1000) for i in range(n_points)]
        
        # 创建有变化的价格数据
        base_price = 100.0
        price_changes = np.random.normal(0, 0.001, n_points)
        prices = [base_price]
        for change in price_changes[1:]:
            prices.append(prices[-1] + change)
        
        test_data = pd.DataFrame({
            'timestamp': timestamps,
            'bid_price': [p - 0.0005 for p in prices],
            'ask_price': [p + 0.0005 for p in prices],
            'last_price': prices
        })
        
        print(f"✅ 测试数据创建: {len(test_data)} 个点")
        
        # 测试AS模型的价差计算
        print("\n🧪 测试AS模型价差计算:")
        
        # 先让AS模型学习一些数据
        for i in range(min(50, len(test_data))):
            row = test_data.iloc[i]
            timestamp = row['timestamp']
            bid_price = row['bid_price']
            ask_price = row['ask_price']
            last_price = row['last_price']
            
            mid_price = (bid_price + ask_price) / 2
            backtest.update_as_model(mid_price, last_price, timestamp)
        
        print("✅ AS模型已学习50个数据点")
        
        # 测试价差计算
        spreads_results = []
        for i in range(50, min(80, len(test_data))):
            row = test_data.iloc[i]
            timestamp = row['timestamp']
            
            # 获取AS模型计算的价差
            buy_spread, sell_spread = backtest.get_optimal_spreads(timestamp)
            spreads_results.append((buy_spread, sell_spread))
            
            if i < 55:  # 只显示前5个结果
                print(f"  数据点 {i}: buy_spread={buy_spread:.6f}, sell_spread={sell_spread:.6f}")
        
        # 分析价差变化
        if spreads_results:
            buy_spreads = [s[0] for s in spreads_results]
            sell_spreads = [s[1] for s in spreads_results]
            
            buy_spread_std = np.std(buy_spreads)
            sell_spread_std = np.std(sell_spreads)
            
            print(f"\n📈 价差变化分析:")
            print(f"买入价差: 平均={np.mean(buy_spreads):.6f}, 标准差={buy_spread_std:.6f}")
            print(f"卖出价差: 平均={np.mean(sell_spreads):.6f}, 标准差={sell_spread_std:.6f}")
            
            # 判断AS模型是否真正起作用
            if buy_spread_std > 0.0001 or sell_spread_std > 0.0001:
                print("✅ AS模型正在动态调整价差！")
                return True
            else:
                print("❌ AS模型可能没有起作用，价差变化太小")
                return False
        else:
            print("❌ 没有获得价差计算结果")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_as_model_status():
    """显示AS模型状态总结"""
    print("\n🔍 AS模型状态检查总结:")
    print("=" * 50)
    
    print("\n✅ 已修复的问题:")
    print("1. 初始化了 buy_estimator 和 sell_estimator")
    print("2. 初始化了 buy_curve 和 sell_curve")
    print("3. 在 update_as_model 中正确调用 on_tick 方法")
    print("4. 添加了AS模型可用性检查")
    print("5. 添加了错误处理机制")
    
    print("\n🎯 AS模型工作流程:")
    print("1. 每个tick调用 update_as_model()")
    print("2. estimator.on_tick() 更新强度估计")
    print("3. get_optimal_spreads() 计算最优价差")
    print("4. 根据市场流动性动态调整价差")
    
    print("\n⚠ 注意事项:")
    print("- AS模型需要一定数据量才能有效工作")
    print("- 价差会根据订单流强度动态变化")
    print("- 如果AS模型不可用，会回退到固定价差")

if __name__ == "__main__":
    print("🧪 测试AS模型是否真正起作用...")
    print("=" * 50)
    
    if test_as_model_functionality():
        print("\n✅ AS模型测试成功！")
        show_as_model_status()
        
        print("\n🚀 现在运行优化应该会:")
        print("python run_optuna_optimization.py")
        print("- AS模型根据市场数据动态调整价差")
        print("- 价差不再是固定值")
        print("- 策略适应性更强")
        
    else:
        print("\n❌ AS模型可能还有问题")
        print("需要进一步检查AS模型的实现")
    
    print("\n💡 建议:")
    print("重新运行优化，观察价差是否有动态变化")
