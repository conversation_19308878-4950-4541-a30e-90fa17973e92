#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试恢复后的绘图功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

def create_test_data():
    """创建测试数据"""
    base_time = datetime.now()
    timestamps = [base_time + timedelta(minutes=i*5) for i in range(50)]
    
    data = pd.DataFrame({
        'timestamp': [int(t.timestamp() * 1000) for t in timestamps],
        'last_price': 100 + np.cumsum(np.random.normal(0, 0.1, 50)),
        'bid_price': 100 + np.cumsum(np.random.normal(0, 0.1, 50)) - 0.01,
        'ask_price': 100 + np.cumsum(np.random.normal(0, 0.1, 50)) + 0.01
    })
    
    return data

def test_simple_plotting():
    """测试简单绘图功能"""
    try:
        from qmt_optuna_backtest import plot_trading_analysis_simple, QMTOptunaBacktest, BacktestConfig, Trade
        print("✅ 模块导入成功")
        
        # 创建测试数据
        test_data = create_test_data()
        print(f"✅ 测试数据: {len(test_data)} 个点")
        
        # 创建回测实例
        config = BacktestConfig(initial_cash=1000000.0)
        backtest = QMTOptunaBacktest(config)
        
        # 添加交易记录
        backtest.trades = [
            Trade(timestamp=test_data['timestamp'].iloc[10], price=100.5, quantity=1000, side='buy', commission=1.0, pnl=0),
            Trade(timestamp=test_data['timestamp'].iloc[30], price=101.0, quantity=1000, side='sell', commission=1.0, pnl=500)
        ]
        
        # 添加权益曲线
        backtest.equity_curve = [
            (test_data['timestamp'].iloc[0], 1000000),
            (test_data['timestamp'].iloc[25], 1000250),
            (test_data['timestamp'].iloc[49], 1000500)
        ]
        
        print("✅ 回测数据准备完成")
        
        # 测试绘图
        save_dir = "test_restored_plots"
        os.makedirs(save_dir, exist_ok=True)
        
        print("📊 开始绘图测试...")
        plot_files = plot_trading_analysis_simple(
            backtest, 
            test_data, 
            save_dir=save_dir, 
            filename_prefix="restored_test"
        )
        
        print(f"📈 绘图结果: {plot_files}")
        
        # 检查文件
        success = False
        if plot_files:
            for plot_type, file_path in plot_files.items():
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    print(f"✅ 文件生成成功: {file_path} ({file_size} bytes)")
                    success = True
                else:
                    print(f"❌ 文件未生成: {file_path}")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 测试恢复后的绘图功能...")
    print("=" * 50)
    
    if test_simple_plotting():
        print("\n✅ 绘图功能恢复成功！")
        print("\n🎉 现在可以正常运行:")
        print("python run_optuna_optimization.py")
        print("\n📊 应该会生成:")
        print("- 价格走势图")
        print("- 交易点标记")
        print("- 收益率曲线")
        print("- PNG格式图片文件")
    else:
        print("\n❌ 绘图功能还有问题")
        print("可能需要检查matplotlib环境")
    
    print("\n💡 提示:")
    print("恢复到了之前简单但可靠的绘图版本")
    print("去掉了复杂的时间轴压缩功能")
    print("应该能正常生成图表了")
