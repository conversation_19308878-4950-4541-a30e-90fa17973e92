"""
Optuna优化后的最佳参数配置 - 自动生成
生成时间: 2025-07-10 00:05:12
优化目标值: -0.234757
训练集收益率: -0.0036
测试集收益率: -0.0060
泛化性能: 1.66
"""

from qmt_optuna_backtest import BacktestConfig

def get_optimized_config() -> BacktestConfig:
    """获取自动优化的最佳参数配置"""
    return BacktestConfig(
        # 基础参数
        initial_cash=1000000.0,
        max_position=56877,
        tick_size=0.001,
        commission_rate=0.0001,

        # 策略核心参数 (自动优化)
        dt=2819,
        window_size=211053,
        order_update_interval=22913,

        # 网格交易参数
        grid_levels=4,
        base_quantity=1136,
        max_spread=0.0031868411173731188,
        min_spread=0.001184854455525527,

        # 原策略参数
        ema_span=0.0067831998304886815,
        ema_spread=-0.0015468803548381819,
        intensity_nspread=3,
        tp_spread=0.001979914312095726,

        # 新增参数
        risk_probility=0.0023568417166358493,
        risk_probility_buy=0.01633398350508689,
        risk_probility_sell=0.027268542549294793,
        enter_lot=5000,
        grid_spread=0.006170792254191168,
        sl_ratio=6.972425054911576,

        # 止盈参数 - 简化版本
        take_profit_levels=[
            (0.001979914312095726, 1.0)  # 使用tp_spread值，全平
        ]
    )

def get_performance_summary():
    """获取优化结果的业绩摘要"""
    return {
        'optimization_timestamp': '20250710_000503',
        'best_objective_value': -0.234757,
        'train_performance': {
            'total_return': -0.0036,
            'sharpe_ratio': 0.0361,
            'max_drawdown': 0.0155,
            'win_rate': 0.3579,
            'profit_factor': 0.5993,
            'total_trades': 623
        },
        'test_performance': {
            'total_return': -0.0060,
            'sharpe_ratio': -0.0626,
            'max_drawdown': 0.0120,
            'win_rate': 0.3832,
            'profit_factor': 0.8112,
            'total_trades': 167
        },
        'generalization_ratio': 1.6628
    }
