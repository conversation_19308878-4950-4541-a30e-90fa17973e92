#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Optuna回测优化脚本
使用backtest_data/merged下的所有数据进行参数优化
"""

import sys
import traceback

def main():
    """运行Optuna优化"""
    import os  # 确保在函数内部可以使用os模块

    print("🚀 开始Optuna参数优化...")
    print("=" * 60)
    
    try:
        # 导入模块
        print("📦 导入模块...")
        from qmt_optuna_backtest import (
            OptimizationConfig, 
            optimize_parameters_with_validation
        )
        print("✅ 模块导入成功")
        
        # 创建优化配置
        print("⚙️ 创建优化配置...")
        opt_config = OptimizationConfig(
            # 基础参数
            initial_cash=1000000.0,
            commission_rate_range=(0.0001, 0.0001),  # 固定手续费
            max_position_range=(50000, 250000),  # 最大持仓范围 5万-15万
            
            # 关闭延时模拟以提高回测速度
            enable_latency=False,
            
            # 策略参数优化范围
            dt_range=(1000, 5000),  # 时间间隔 1-3秒
            window_size_range=(180000, 600000),  # 评估窗口 3-5分钟
            order_update_interval_range=(9000, 30000),  # 挂单更新间隔 15-30秒
            
            # 网格参数（新增）
            enter_lot_range=(5000, 5000),  # 初始买入手数
            grid_spread_range=(0.004, 0.012),  # 网格间距
            
            # 风险偏好参数（新增）
            risk_probility_range=(0.0001, 0.05),      # 通用风险偏好 2%-6%
            risk_probility_buy_range=(0.05, 0.05), # 买入风险偏好 1.5%-5%
            risk_probility_sell_range=(0.1, 0.1), # 卖出风险偏好 2.5%-7%
            
            # 止盈参数 - 一层止盈
            tp_spread_range=(0.005, 0.012),  # 止盈价差 0.2%-0.8%
            
            # 优化设置（增加试验次数）
            n_trials=1,  # 增加到20次试验
            n_jobs=1,     # 单线程运行
            timeout=None,  # 无超时限制
            sl_ratio_range=(2, 8)
        )
        print("✅ 优化配置创建成功")
        
        print("📊 优化配置:")
        print(f"  - 试验次数: {opt_config.n_trials}")
        print(f"  - 数据目录: backtest_data/merged")
        print(f"  - 延时模拟: {'开启' if opt_config.enable_latency else '关闭'}")
        print(f"  - 最大持仓范围: {opt_config.max_position_range}")
        print(f"  - 风险偏好范围: {opt_config.risk_probility_range}")
        print(f"  - 网格间距范围: {opt_config.grid_spread_range}")
        print(f"  - 初始手数范围: {opt_config.enter_lot_range}")
        print("=" * 60)
        
        # 检查数据目录
        print("📁 检查数据目录...")
        data_dir = "backtest_data/merged"
        if not os.path.exists(data_dir):
            print(f"❌ 数据目录不存在: {data_dir}")
            return None
        
        files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]
        print(f"✅ 找到 {len(files)} 个数据文件")
        for f in files[:3]:  # 显示前3个文件
            print(f"  - {f}")
        if len(files) > 3:
            print(f"  ... 还有 {len(files)-3} 个文件")
        
        # === 新增：检查是否有现有的studies ===
        from qmt_optuna_backtest import list_existing_studies
        existing_studies = list_existing_studies()

        auto_continue = False
        if existing_studies:
            print(f"\n📚 发现 {len(existing_studies)} 个现有的优化研究")
            print(f"最新的研究: {existing_studies[0]}")

            # 可以在这里添加用户选择逻辑，现在默认自动继续
            auto_continue = True  # 启用自动继续最新的优化研究

        # 运行优化
        if auto_continue:
            print("\n🔄 自动继续最新的优化研究...")
        else:
            print("\n🔄 开始新的优化...")

        results = optimize_parameters_with_validation(
            data_dir=data_dir,
            opt_config=opt_config,
            train_ratio=0.7,  # 70%训练，30%测试
            auto_continue=auto_continue  # 新增：自动继续参数
        )
        
        print("\n🎉 优化完成！")
        print("=" * 60)
        print("📈 最佳结果:")
        
        if results and 'best_params' in results:
            print("\n🏆 最佳参数:")
            for key, value in results['best_params'].items():
                if isinstance(value, float):
                    print(f"  {key}: {value:.6f}")
                else:
                    print(f"  {key}: {value}")
        
        if results and 'best_value' in results:
            print(f"\n💰 最佳目标值: {results['best_value']:.6f}")
        
        if results and 'test_results' in results:
            test_res = results['test_results']
            print(f"\n📊 测试集表现:")
            print(f"  总收益率: {test_res.get('total_return', 0):.4f} ({test_res.get('total_return', 0)*100:.2f}%)")
            print(f"  夏普比率: {test_res.get('sharpe_ratio', 0):.4f}")
            print(f"  最大回撤: {test_res.get('max_drawdown', 0):.4f} ({test_res.get('max_drawdown', 0)*100:.2f}%)")
            print(f"  总交易次数: {test_res.get('total_trades', 0)}")
            print(f"  📈 胜率 (盈利交易比例): {test_res.get('win_rate', 0):.4f} ({test_res.get('win_rate', 0)*100:.2f}%)")
            print(f"  💰 盈亏比 (总盈利/总亏损): {test_res.get('profit_factor', 0):.4f}")
            print(f"  ✅ 盈利交易次数: {test_res.get('winning_trades', 0)}")
            print(f"  ❌ 亏损交易次数: {test_res.get('losing_trades', 0)}")

            # 计算止损率（基于现有数据的估算）
            total_trades = test_res.get('total_trades', 0)
            losing_trades = test_res.get('losing_trades', 0)
            if total_trades > 0:
                estimated_stop_loss_rate = losing_trades / total_trades
                estimated_take_profit_rate = test_res.get('winning_trades', 0) / total_trades
                print(f"  🛑 估算止损率: {estimated_stop_loss_rate:.4f} ({estimated_stop_loss_rate*100:.2f}%)")
                print(f"  🎯 估算止盈率: {estimated_take_profit_rate:.4f} ({estimated_take_profit_rate*100:.2f}%)")
        
        print("=" * 40)

        # === 检查保存结果 ===
        if 'save_directory' in results:
            save_dir = results['save_directory']
            print(f"✅ 优化结果已保存到: {save_dir}")

            # 检查保存的文件
            if os.path.exists(save_dir):
                files = os.listdir(save_dir)
                print(f"📂 保存了 {len(files)} 个文件:")
                for file in sorted(files)[:3]:  # 显示前3个文件
                    print(f"  📄 {file}")
                if len(files) > 3:
                    print(f"  ... 还有 {len(files)-3} 个文件")
            else:
                print(f"❌ 保存目录不存在: {save_dir}")
        else:
            print("⚠ 没有找到保存信息，可能保存过程中出现了问题")
            print("💡 请检查上面的输出中是否有保存相关的错误信息")

        return results
        
    except Exception as e:
        print(f"❌ 优化过程中出现错误: {e}")
        traceback.print_exc()
        return None

if __name__ == "__main__":
    results = main() 