#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试快速绘图功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time

def create_test_data(size=1000):
    """创建测试数据"""
    base_time = datetime.now()
    timestamps = [base_time + timedelta(minutes=i) for i in range(size)]
    
    data = pd.DataFrame({
        'timestamp': [int(t.timestamp() * 1000) for t in timestamps],
        'datetime': timestamps,
        'last_price': 100 + np.cumsum(np.random.normal(0, 0.1, size)),
        'bid_price': 100 + np.cumsum(np.random.normal(0, 0.1, size)) - 0.01,
        'ask_price': 100 + np.cumsum(np.random.normal(0, 0.1, size)) + 0.01
    })
    
    return data

def test_plotting_performance():
    """测试绘图性能"""
    try:
        from qmt_optuna_backtest import plot_trading_analysis_simple, QMTOptunaBacktest, BacktestConfig, Trade
        print('✅ 简化绘图函数导入成功')
        
        # 创建测试数据
        print('📊 创建测试数据...')
        test_data = create_test_data(2000)  # 2000个数据点
        
        # 创建测试回测实例
        config = BacktestConfig(initial_cash=1000000.0)
        backtest = QMTOptunaBacktest(config)
        
        # 添加一些模拟交易
        backtest.trades = [
            Trade(timestamp=test_data['timestamp'].iloc[100], side='buy', price=100.5, quantity=1000, pnl=0, reason='test'),
            Trade(timestamp=test_data['timestamp'].iloc[500], side='sell', price=101.0, quantity=1000, pnl=500, reason='test'),
            Trade(timestamp=test_data['timestamp'].iloc[1000], side='buy', price=100.8, quantity=1000, pnl=0, reason='test'),
            Trade(timestamp=test_data['timestamp'].iloc[1500], side='sell', price=101.5, quantity=1000, pnl=700, reason='test')
        ]
        
        # 添加权益曲线
        backtest.equity_curve = [
            (test_data['timestamp'].iloc[i], 1000000 + i * 100) 
            for i in range(0, len(test_data), 50)
        ]
        
        print('✅ 测试数据创建完成')
        
        # 测试绘图性能
        print('📈 开始绘图性能测试...')
        start_time = time.time()
        
        plot_files = plot_trading_analysis_simple(
            backtest, 
            test_data, 
            save_dir="test_plots", 
            filename_prefix="performance_test"
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f'✅ 绘图完成，耗时: {duration:.2f} 秒')
        print(f'📂 生成文件: {len(plot_files)} 个')
        
        for plot_type, file_path in plot_files.items():
            print(f'  📄 {plot_type}: {file_path}')
        
        # 性能评估
        if duration < 5:
            print('🚀 性能优秀: 绘图速度很快')
        elif duration < 15:
            print('✅ 性能良好: 绘图速度可接受')
        else:
            print('⚠ 性能一般: 绘图速度较慢')
        
        return True
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()
        return False

def show_optimization_summary():
    """显示优化总结"""
    print("\n🎨 绘图性能优化总结:")
    print("=" * 50)
    
    print("\n⚡ 性能优化措施:")
    print("1. 📉 数据采样: 大于1000个点时自动采样")
    print("2. 🎯 交易点限制: 最多显示20个买卖点")
    print("3. 📊 简化图表: 只生成核心的价格和收益图")
    print("4. 🖼️ 降低DPI: 150 DPI平衡质量和速度")
    print("5. ⏱️ 超时保护: 30秒超时避免卡死")
    print("6. 🧹 资源清理: 自动关闭图表释放内存")
    
    print("\n📈 图表特点:")
    print("✅ 快速生成: 通常5秒内完成")
    print("✅ 稳定可靠: 避免复杂计算导致的问题")
    print("✅ 核心信息: 显示最重要的价格和收益数据")
    print("✅ 交易点: 清晰标记买卖位置")
    
    print("\n🔧 技术改进:")
    print("- 移除复杂的时间轴压缩算法")
    print("- 简化交易点映射逻辑")
    print("- 减少图表数量和复杂度")
    print("- 添加数据量检查和限制")

if __name__ == "__main__":
    print("🧪 测试快速绘图功能...")
    
    if test_plotting_performance():
        print("\n✅ 绘图性能测试通过！")
        show_optimization_summary()
        
        print("\n🚀 现在运行优化应该很快:")
        print("python run_optuna_optimization.py")
        
        print("\n📊 预期效果:")
        print("- 绘图过程不会卡住")
        print("- 5-15秒内完成图表生成")
        print("- 生成简洁清晰的分析图表")
        print("- 包含价格走势和收益曲线")
    else:
        print("\n❌ 测试失败，需要进一步调试")
