# QMT策略Optuna参数优化使用指南

## 概述

本工具使用Optuna自动优化QMT做市商策略的关键参数，特别是用户关心的`enter_lot`（入场手数）和`tp_spread`（止盈价差）等参数。

## 主要文件

- `qmt_optuna_backtest.py` - 带Optuna优化功能的回测引擎
- `optuna_example.py` - 使用示例和交互式界面
- `backtest_data/` - 存放回测数据的目录

## 核心优化参数

### 用户重点关心的参数
- **enter_lot**: 入场手数 (范围: 1000-8000)
- **tp_spread**: 止盈价差 (范围: 0.001-0.008)
- **max_position**: 最大持仓 (范围: 5000-15000)
- **commission_rate**: 手续费率 (范围: 0.0001-0.0005)

### AS模型参数
- **gamma**: 风险厌恶系数
- **window_size**: 评估窗口大小
- **grid_levels**: 网格层数
- **max_spread/min_spread**: 价差范围

## 快速开始

### 1. 环境准备
```bash
# 确保安装了必要的依赖
pip install optuna pandas numpy

# 或者使用项目的requirements.txt
pip install -r requirements.txt
```

### 2. 准备数据
确保在`backtest_data/`目录下有数据文件，例如：
- `sh513120_20240909.csv`
- `sh513120_20240910.csv`
- `sh513120_20240911.csv`

### 3. 运行优化
```bash
cd etf_mm
python optuna_example.py
```

选择优化模式：
- **模式1**: 快速优化 - 重点优化enter_lot和tp_spread (30次试验，30分钟)
- **模式2**: 全面优化 - 优化所有参数 (100次试验，1小时)

## 使用示例

### 快速优化示例
```python
from qmt_optuna_backtest import OptimizationConfig, optimize_parameters

# 配置优化参数
opt_config = OptimizationConfig(
    enter_lot_range=(1000, 8000),      # 入场手数范围
    tp_spread_range=(0.001, 0.008),    # 止盈价差范围
    n_trials=30,                       # 试验次数
    timeout=1800                       # 30分钟超时
)

# 运行优化
results = optimize_parameters("backtest_data/sh513120_20240909.csv", opt_config)

# 查看结果
print(f"最佳enter_lot: {results['best_params']['enter_lot']}")
print(f"最佳tp_spread: {results['best_params']['tp_spread']}")
print(f"总收益率: {results['final_results']['total_return']:.4f}")
```

### 自定义参数范围
```python
# 自定义优化配置
custom_config = OptimizationConfig(
    # 核心参数
    enter_lot_range=(2000, 6000),      # 缩小范围
    tp_spread_range=(0.002, 0.006),    # 缩小范围
    
    # 其他参数
    max_position_range=(8000, 12000),
    gamma_range=(0.1, 0.2),            # 更保守的风险参数
    
    # 优化设置
    n_trials=50,
    n_jobs=1,
    timeout=2400  # 40分钟
)
```

## 优化结果解读

### 输出示例
```
=== 优化结果汇总 ===
【最佳参数】
📊 enter_lot (入场手数): 4500
🎯 tp_spread (止盈价差): 0.0035
💰 max_position (最大持仓): 12000
⚡ commission_rate (手续费率): 0.000250

【回测结果】
📈 总收益率: 0.0850 (8.50%)
📊 夏普比率: 1.2500
📉 最大回撤: 0.0320 (3.20%)
🔄 总交易次数: 245
🎯 胜率: 0.6200 (62.00%)
💵 最终权益: 1085000.00
```

### 关键指标说明
- **总收益率**: 策略的总体收益表现
- **夏普比率**: 风险调整后的收益指标，越高越好
- **最大回撤**: 最大亏损幅度，越小越好
- **胜率**: 盈利交易占比
- **目标函数值**: 综合优化指标 = 收益率 × (1+夏普比率) × (1-最大回撤)

## 高级功能

### 多文件优化
```python
# 对多个数据文件进行优化比较
data_files = [
    "backtest_data/sh513120_20240909.csv",
    "backtest_data/sh513120_20240910.csv", 
    "backtest_data/sh513120_20240911.csv"
]

# 会自动分析不同数据文件的最佳参数差异
```

### 结果保存
优化结果会自动保存到`optimization_results.txt`文件中，包含：
- 最佳参数列表
- 详细回测结果
- 目标函数值

### 参数稳定性分析
通过多文件优化可以观察参数的稳定性：
- 如果不同数据文件得到相似的最佳参数，说明参数较为稳定
- 如果差异较大，可能需要调整参数范围或优化策略

## 注意事项

1. **数据质量**: 确保回测数据质量良好，包含完整的买卖盘口信息
2. **计算时间**: 全面优化可能需要较长时间，建议先用快速模式测试
3. **过拟合风险**: 避免在单一数据文件上过度优化，建议使用多文件验证
4. **参数范围**: 根据实际交易经验设置合理的参数范围
5. **市场环境**: 不同市场环境下的最佳参数可能不同，需要定期重新优化

## 故障排除

### 常见问题
1. **数据文件不存在**: 检查`backtest_data/`目录下是否有数据文件
2. **内存不足**: 减少`n_trials`数量或使用更小的数据文件
3. **优化时间过长**: 设置合理的`timeout`参数
4. **AS API导入失败**: 检查`../utils/AS_api/src`路径是否正确

### 调试模式
```python
# 启用详细日志
import optuna
optuna.logging.set_verbosity(optuna.logging.INFO)

# 减少试验次数进行快速测试
opt_config = OptimizationConfig(n_trials=5)
```

## 扩展功能

可以根据需要扩展更多参数的优化，例如：
- 风控参数（止损阈值、最大持仓时间等）
- 技术指标参数（移动平均周期、RSI参数等）
- 交易时间参数（开盘时间、收盘时间等）

只需在`OptimizationConfig`中添加相应的参数范围，并在`create_config_from_trial`函数中处理即可。 