#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试保存功能
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime

def check_save_function():
    """检查保存功能是否正常"""
    try:
        from qmt_optuna_backtest import save_optimization_results, QMTOptunaBacktest, BacktestConfig
        print('✅ 保存函数导入成功')
        
        # 创建测试结果
        test_results = {
            'best_params': {'dt': 2000, 'tp_spread': 0.004, 'sl_ratio': 4.0},
            'best_value': 0.15,
            'train_results': {
                'total_return': 0.12, 
                'sharpe_ratio': 1.5,
                'max_drawdown': 0.05,
                'total_trades': 10,
                'win_rate': 0.6,
                'profit_factor': 1.8
            },
            'test_results': {
                'total_return': 0.10, 
                'sharpe_ratio': 1.2,
                'max_drawdown': 0.06,
                'total_trades': 8,
                'win_rate': 0.55,
                'profit_factor': 1.6
            },
            'generalization_ratio': 0.83,
            'study_name': 'test_study',
            'storage_url': 'sqlite:///test.db',
            'n_trials_completed': 4
        }
        
        # 创建测试数据
        timestamps = range(1000000, 1010000, 1000)
        test_data = pd.DataFrame({
            'timestamp': timestamps,
            'datetime': [pd.to_datetime(ts, unit='ms') for ts in timestamps],
            'bid_price': [10.0 + i*0.001 for i in range(10)],
            'ask_price': [10.001 + i*0.001 for i in range(10)],
            'last_price': [10.0005 + i*0.001 for i in range(10)]
        })
        
        # 创建测试回测实例
        config = BacktestConfig(initial_cash=1000000.0, sl_ratio=4.0)
        backtest = QMTOptunaBacktest(config)
        
        # 添加一些模拟交易
        from qmt_optuna_backtest import Trade
        backtest.trades = [
            Trade(timestamp=1000000, side='buy', price=10.0, quantity=1000, pnl=0, reason='initial'),
            Trade(timestamp=1005000, side='sell', price=10.05, quantity=1000, pnl=50, reason='take_profit')
        ]
        
        # 添加权益曲线
        backtest.equity_curve = [
            (1000000, 1000000),
            (1005000, 1000050)
        ]
        
        print('✅ 测试数据创建成功')
        
        # 测试保存功能
        print('\n📁 测试保存功能...')
        save_dir = save_optimization_results(
            test_results,
            best_backtest=backtest,
            test_data=test_data,
            save_plots=True,
            save_config=True
        )
        
        print(f'✅ 保存成功到: {save_dir}')
        
        # 检查生成的文件
        if os.path.exists(save_dir):
            files = os.listdir(save_dir)
            print(f'\n📂 生成的文件 ({len(files)} 个):')
            for file in sorted(files):
                file_path = os.path.join(save_dir, file)
                size = os.path.getsize(file_path)
                print(f'  📄 {file} ({size} bytes)')
        else:
            print(f'❌ 保存目录不存在: {save_dir}')
        
        return True
        
    except Exception as e:
        print(f'❌ 保存功能测试失败: {e}')
        import traceback
        traceback.print_exc()
        return False

def check_directories():
    """检查相关目录"""
    print('\n📁 检查目录状态:')
    
    dirs_to_check = [
        'optimization_results',
        'optuna_studies'
    ]
    
    for dir_name in dirs_to_check:
        if os.path.exists(dir_name):
            files = os.listdir(dir_name)
            print(f'✅ {dir_name}: {len(files)} 个文件')
            if files:
                print(f'   最新文件: {sorted(files)[-1]}')
        else:
            print(f'❌ {dir_name}: 目录不存在')

def check_recent_run():
    """检查最近的运行结果"""
    print('\n🔍 检查最近的优化结果:')
    
    results_dir = 'optimization_results'
    if os.path.exists(results_dir):
        files = os.listdir(results_dir)
        if files:
            # 按修改时间排序
            files_with_time = []
            for file in files:
                file_path = os.path.join(results_dir, file)
                mtime = os.path.getmtime(file_path)
                files_with_time.append((file, mtime))
            
            files_with_time.sort(key=lambda x: x[1], reverse=True)
            
            print(f'📊 最近的 5 个文件:')
            for file, mtime in files_with_time[:5]:
                time_str = datetime.fromtimestamp(mtime).strftime('%Y-%m-%d %H:%M:%S')
                print(f'  📄 {file} - {time_str}')
        else:
            print('📭 optimization_results 目录为空')
    else:
        print('❌ optimization_results 目录不存在')

if __name__ == "__main__":
    print("🔧 调试保存功能...")
    print("=" * 50)
    
    # 检查目录
    check_directories()
    
    # 检查最近运行
    check_recent_run()
    
    # 测试保存功能
    print("\n" + "=" * 50)
    if check_save_function():
        print("\n✅ 保存功能正常工作!")
    else:
        print("\n❌ 保存功能有问题")
    
    print("\n💡 如果运行优化后没有看到保存结果，请检查:")
    print("1. 是否有错误信息输出")
    print("2. optimization_results 目录是否存在")
    print("3. 是否有权限问题")
    print("4. 磁盘空间是否足够")
