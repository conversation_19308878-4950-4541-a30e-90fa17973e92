"""
Optuna优化后的最佳参数配置 - 自动生成
生成时间: 2025-07-09 23:32:52
优化目标值: -0.181851
训练集收益率: -0.0026
测试集收益率: -0.0063
泛化性能: 2.43
"""

from qmt_optuna_backtest import BacktestConfig

def get_optimized_config() -> BacktestConfig:
    """获取自动优化的最佳参数配置"""
    return BacktestConfig(
        # 基础参数
        initial_cash=1000000.0,
        max_position=69902,
        tick_size=0.001,
        commission_rate=0.0001,

        # 策略核心参数 (自动优化)
        dt=1456,
        window_size=219354,
        order_update_interval=17084,

        # 网格交易参数
        grid_levels=6,
        base_quantity=1252,
        max_spread=0.003689782003224768,
        min_spread=0.0015033515006029677,

        # 原策略参数
        ema_span=0.009866500926261694,
        ema_spread=-0.001976142055218951,
        intensity_nspread=5,
        tp_spread=0.0025483278875033065,

        # 新增参数
        risk_probility=0.023597383134229502,
        risk_probility_buy=0.037574762077025044,
        risk_probility_sell=0.01918125675188219,
        enter_lot=5000,
        grid_spread=0.00965755582853081,
        sl_ratio=4.169567220141294,

        # 止盈参数 - 简化版本
        take_profit_levels=[
            (0.0025483278875033065, 1.0)  # 使用tp_spread值，全平
        ]
    )

def get_performance_summary():
    """获取优化结果的业绩摘要"""
    return {
        'optimization_timestamp': '20250709_233243',
        'best_objective_value': -0.181851,
        'train_performance': {
            'total_return': -0.0026,
            'sharpe_ratio': 0.0326,
            'max_drawdown': 0.0143,
            'win_rate': 0.3185,
            'profit_factor': 0.6392,
            'total_trades': 471
        },
        'test_performance': {
            'total_return': -0.0063,
            'sharpe_ratio': -0.1082,
            'max_drawdown': 0.0121,
            'win_rate': 0.3125,
            'profit_factor': 0.6380,
            'total_trades': 112
        },
        'generalization_ratio': 2.4277
    }
