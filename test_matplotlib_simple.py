#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试matplotlib
"""

import os

def test_matplotlib_basic():
    """基础matplotlib测试"""
    try:
        import matplotlib
        matplotlib.use('Agg')  # 非交互式后端
        import matplotlib.pyplot as plt
        
        print(f"✅ matplotlib版本: {matplotlib.__version__}")
        print(f"✅ 后端: {matplotlib.get_backend()}")
        
        # 创建简单图表
        fig, ax = plt.subplots(1, 1, figsize=(8, 6))
        ax.plot([1, 2, 3, 4], [1, 4, 2, 3], 'b-', linewidth=2, label='测试线')
        ax.scatter([2, 3], [4, 2], color='red', s=100, label='测试点')
        ax.set_title('matplotlib测试图表')
        ax.set_xlabel('X轴')
        ax.set_ylabel('Y轴')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 保存图表
        test_file = "matplotlib_test.png"
        plt.savefig(test_file, dpi=150, bbox_inches='tight')
        plt.close()
        
        # 检查文件
        if os.path.exists(test_file):
            file_size = os.path.getsize(test_file)
            print(f"✅ 图表保存成功: {test_file} ({file_size} bytes)")
            
            # 清理测试文件
            os.remove(test_file)
            return True
        else:
            print("❌ 图表文件未生成")
            return False
            
    except Exception as e:
        print(f"❌ matplotlib测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_directory_permissions():
    """测试目录权限"""
    try:
        test_dir = "test_permissions"
        os.makedirs(test_dir, exist_ok=True)
        
        test_file = os.path.join(test_dir, "test.txt")
        with open(test_file, 'w') as f:
            f.write("test")
        
        if os.path.exists(test_file):
            print(f"✅ 目录权限正常: {test_dir}")
            os.remove(test_file)
            os.rmdir(test_dir)
            return True
        else:
            print(f"❌ 目录权限问题: {test_dir}")
            return False
            
    except Exception as e:
        print(f"❌ 权限测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🔧 matplotlib和权限测试...")
    print("=" * 40)
    
    # 测试目录权限
    perm_ok = test_directory_permissions()
    
    # 测试matplotlib
    mpl_ok = test_matplotlib_basic()
    
    print("\n" + "=" * 40)
    
    if perm_ok and mpl_ok:
        print("✅ 所有测试通过！")
        print("matplotlib和文件权限都正常")
        print("\n💡 如果优化时还是没有图表，可能是:")
        print("1. 绘图函数内部有其他错误")
        print("2. 数据格式问题")
        print("3. 绘图被跳过了")
    else:
        print("❌ 有问题需要解决:")
        if not perm_ok:
            print("- 文件权限问题")
        if not mpl_ok:
            print("- matplotlib问题")
    
    print("\n🚀 建议:")
    print("1. 重新运行 python run_optuna_optimization.py")
    print("2. 仔细查看输出中的绘图相关信息")
    print("3. 检查是否有错误信息")
