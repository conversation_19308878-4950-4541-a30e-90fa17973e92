#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试按日期绘图功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

def create_multi_day_test_data():
    """创建多天的测试数据"""
    all_data = []
    base_price = 100.0
    
    # 创建3天的数据
    for day in range(3):
        base_date = datetime.now().replace(hour=9, minute=30, second=0, microsecond=0) + timedelta(days=day)
        
        # 每天的交易时间段
        time_segments = [
            (base_date, base_date + timedelta(hours=2)),  # 上午 9:30-11:30
            (base_date + timedelta(hours=3.5), base_date + timedelta(hours=5.5))  # 下午 13:00-15:00
        ]
        
        for start_time, end_time in time_segments:
            duration_minutes = int((end_time - start_time).total_seconds() / 60)
            times = [start_time + timedelta(minutes=i*5) for i in range(duration_minutes//5)]
            
            # 生成价格数据
            n_points = len(times)
            price_changes = np.random.normal(0, 0.05, n_points)
            prices = [base_price]
            for change in price_changes[1:]:
                prices.append(prices[-1] + change)
            
            # 创建数据段
            segment_data = pd.DataFrame({
                'timestamp': [int(t.timestamp() * 1000) for t in times],
                'last_price': prices[:len(times)],
                'bid_price': [p - 0.01 for p in prices[:len(times)]],
                'ask_price': [p + 0.01 for p in prices[:len(times)]]
            })
            
            all_data.append(segment_data)
        
        base_price = prices[-1] if prices else base_price  # 延续价格
    
    # 合并所有数据
    data = pd.concat(all_data, ignore_index=True)
    return data

def test_daily_plotting():
    """测试按日期绘图功能"""
    try:
        from qmt_optuna_backtest import plot_daily_analysis, QMTOptunaBacktest, BacktestConfig, Trade
        print("✅ 模块导入成功")
        
        # 创建多天测试数据
        test_data = create_multi_day_test_data()
        print(f"✅ 测试数据: {len(test_data)} 个点，跨越多天")
        
        # 检查数据的日期分布
        test_data['datetime'] = pd.to_datetime(test_data['timestamp'], unit='ms')
        dates = test_data['datetime'].dt.date.unique()
        print(f"📅 数据包含日期: {list(dates)}")
        
        # 创建回测实例
        config = BacktestConfig(initial_cash=1000000.0)
        backtest = QMTOptunaBacktest(config)
        
        # 为每天添加一些交易记录
        trades = []
        equity_curve = []
        
        for i, date in enumerate(dates):
            day_data = test_data[test_data['datetime'].dt.date == date]
            if len(day_data) > 10:
                # 每天添加几笔交易
                buy_idx = len(day_data) // 4
                sell_idx = 3 * len(day_data) // 4
                
                trades.extend([
                    Trade(timestamp=day_data.iloc[buy_idx]['timestamp'], 
                         price=day_data.iloc[buy_idx]['last_price'], 
                         quantity=1000, side='buy', commission=1.0, pnl=0),
                    Trade(timestamp=day_data.iloc[sell_idx]['timestamp'], 
                         price=day_data.iloc[sell_idx]['last_price'], 
                         quantity=1000, side='sell', commission=1.0, pnl=100 + i*50)
                ])
                
                # 每天添加权益曲线点
                equity_curve.extend([
                    (day_data.iloc[0]['timestamp'], 1000000 + i*200),
                    (day_data.iloc[len(day_data)//2]['timestamp'], 1000000 + i*200 + 150),
                    (day_data.iloc[-1]['timestamp'], 1000000 + i*200 + 100 + i*50)
                ])
        
        backtest.trades = trades
        backtest.equity_curve = equity_curve
        
        print(f"✅ 回测数据: {len(trades)} 笔交易，{len(equity_curve)} 个权益点")
        
        # 测试按日期绘图
        save_dir = "test_daily_plots"
        os.makedirs(save_dir, exist_ok=True)
        
        print("📊 开始按日期绘图测试...")
        plot_files = plot_daily_analysis(
            backtest, 
            test_data, 
            save_dir=save_dir, 
            filename_prefix="test_daily"
        )
        
        print(f"📈 按日期绘图结果: {len(plot_files)} 个文件")
        
        # 检查生成的文件
        success_count = 0
        for plot_type, file_path in plot_files.items():
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                print(f"✅ {plot_type}: {file_path} ({file_size} bytes)")
                success_count += 1
            else:
                print(f"❌ 文件未生成: {file_path}")
        
        print(f"\n📊 总结:")
        print(f"  预期日期数: {len(dates)}")
        print(f"  生成图表数: {len(plot_files)}")
        print(f"  成功文件数: {success_count}")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 测试按日期绘图功能...")
    print("=" * 50)
    
    if test_daily_plotting():
        print("\n✅ 按日期绘图功能测试成功！")
        print("\n🎉 新功能特点:")
        print("📅 按交易日分别生成图表")
        print("📊 每日独立的价格走势图")
        print("📈 每日独立的收益变化图")
        print("🎯 每日交易点精确标记")
        print("📋 每日收益统计信息")
        
        print("\n🚀 现在运行优化会生成:")
        print("python run_optuna_optimization.py")
        print("- overall_YYYYMMDD_HHMMSS.png (总体图表)")
        print("- daily_2024-XX-XX_YYYYMMDD_HHMMSS.png (每日图表)")
        
    else:
        print("\n❌ 按日期绘图功能测试失败")
    
    print("\n💡 优势:")
    print("- 可以清楚看到策略在每个交易日的表现")
    print("- 便于发现特定日期的问题")
    print("- 更好地分析日内交易模式")
    print("- 每日收益统计一目了然")
