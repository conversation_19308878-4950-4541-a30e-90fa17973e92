#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试绘图功能
"""

def test_import():
    """测试导入"""
    try:
        from qmt_optuna_backtest import plot_trading_analysis_simple
        print('✅ 简化绘图函数导入成功')
        return True
    except Exception as e:
        print(f'❌ 导入失败: {e}')
        return False

if __name__ == "__main__":
    print("🧪 快速测试...")
    
    if test_import():
        print("\n✅ 绘图优化完成！")
        print("\n🎨 主要改进:")
        print("1. ⚡ 数据采样: 大数据量时自动采样到1000个点")
        print("2. 🎯 交易点限制: 最多显示20个买卖点")
        print("3. 📊 简化图表: 只生成核心图表")
        print("4. ⏱️ 超时保护: 30秒超时避免卡死")
        print("5. 🧹 资源清理: 自动释放内存")
        
        print("\n🚀 现在运行优化应该很快:")
        print("python run_optuna_optimization.py")
        
        print("\n📈 预期效果:")
        print("- 绘图不会长时间卡住")
        print("- 快速生成简洁的分析图表")
        print("- 包含价格走势和收益曲线")
        print("- 清晰标记买卖点")
    else:
        print("\n❌ 还有问题需要解决")
