#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证保存功能修复
"""

def check_save_in_continue_functions():
    """检查继续优化函数中是否有保存功能"""
    try:
        # 读取文件内容
        with open('qmt_optuna_backtest.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查continue_optimization函数中是否有保存代码
        if 'save_optimization_results' in content and '继续优化结果已保存' in content:
            print('✅ continue_optimization函数中已添加保存功能')
        else:
            print('❌ continue_optimization函数中缺少保存功能')
            return False
        
        # 检查主优化函数中是否有保存代码
        if '开始保存优化结果' in content:
            print('✅ 主优化函数中已有保存功能')
        else:
            print('❌ 主优化函数中缺少保存功能')
            return False
        
        return True
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')
        return False

def check_auto_continue_setting():
    """检查auto_continue设置"""
    try:
        with open('run_optuna_optimization.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'auto_continue = True' in content:
            print('✅ auto_continue已启用，会调用continue_optimization')
            return True
        else:
            print('ℹ️ auto_continue未启用，会调用主优化函数')
            return False
            
    except Exception as e:
        print(f'❌ 检查失败: {e}')
        return False

if __name__ == "__main__":
    print("🔍 验证保存功能修复...")
    print("=" * 50)
    
    # 检查保存功能
    save_ok = check_save_in_continue_functions()
    
    # 检查auto_continue设置
    auto_continue_enabled = check_auto_continue_setting()
    
    print("\n" + "=" * 50)
    
    if save_ok:
        print("✅ 保存功能修复完成！")
        
        if auto_continue_enabled:
            print("\n📋 当前流程:")
            print("1. 检测到现有studies")
            print("2. auto_continue=True")
            print("3. 调用continue_latest_optimization")
            print("4. 调用continue_optimization")
            print("5. 执行保存功能 ← 新增")
            
        print("\n🚀 现在运行 python run_optuna_optimization.py")
        print("应该会看到保存相关的输出信息！")
        
    else:
        print("❌ 保存功能还有问题")
        
    print("\n💡 预期输出:")
    print("============================================================")
    print("📁 开始保存继续优化的结果...")
    print("📊 最佳参数数量: X")
    print("📈 最佳目标值: X.XXXXXX")
    print("🔢 完成试验数: X")
    print("🔄 继续自: etf_mm_optimization_XXXXXXXX_XXXXXX")
    print("➕ 新增试验: X")
    print("💾 调用保存函数...")
    print("✅ 继续优化结果已保存到: optimization_results")
    print("============================================================")
