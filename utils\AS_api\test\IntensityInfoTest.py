import pytest
from utils.AS_api.src import IntensityInfo

"""
测试与 A, k, λ 和 δ 相关的计算
"""

class TestIntensityInfo:
    """
    测试 IntensityInfo 类
    """
    
    def __init__(self):
        """初始化测试数据"""
        self.intensity = 0.00024
        self.spread = 0.00002
        self.a = 0.00037
        self.k = 20600
        self.eps = 1e-10

    def test_get_intensity(self):
        """测试 getIntensity 方法"""
        test_spread = IntensityInfo.get_spread(self.intensity, self.a, self.k)
        test_intensity = IntensityInfo.get_intensity(test_spread, self.a, self.k)
        assert abs(test_intensity - self.intensity) < self.eps

    def test_get_spread(self):
        """测试 getSpread 方法"""
        test_intensity = IntensityInfo.get_intensity(self.spread, self.a, self.k)
        test_spread = IntensityInfo.get_spread(test_intensity, self.a, self.k)
        assert abs(test_spread - self.spread) < self.eps

    def test_intensity_info_object(self):
        """测试 IntensityInfo 对象"""
        ii = IntensityInfo(self.a, self.k, self.a, self.k)
        test_spread = IntensityInfo.get_spread(self.intensity, self.a, self.k)
        test_intensity = IntensityInfo.get_intensity(self.spread, self.a, self.k)
        
        assert abs(ii.get_buy_fill_intensity(self.spread) - test_intensity) < self.eps
        assert abs(ii.get_sell_fill_intensity(self.spread) - test_intensity) < self.eps
        assert abs(ii.get_buy_spread(self.intensity) - test_spread) < self.eps
        assert abs(ii.get_sell_spread(self.intensity) - test_spread) < self.eps
