"""
Optuna优化后的最佳参数配置 - 自动生成
生成时间: 2025-07-10 00:58:23
优化目标值: -0.195355
训练集收益率: -0.0029
测试集收益率: -0.0060
泛化性能: 2.08
"""

from qmt_optuna_backtest import BacktestConfig

def get_optimized_config() -> BacktestConfig:
    """获取自动优化的最佳参数配置"""
    return BacktestConfig(
        # 基础参数
        initial_cash=1000000.0,
        max_position=82868,
        tick_size=0.001,
        commission_rate=0.0001,

        # 策略核心参数 (自动优化)
        dt=1483,
        window_size=234429,
        order_update_interval=26959,

        # 网格交易参数
        grid_levels=4,
        base_quantity=1155,
        max_spread=0.003669621670929033,
        min_spread=0.0014475499962778942,

        # 原策略参数
        ema_span=0.009551637793287658,
        ema_spread=-0.003103829796140749,
        intensity_nspread=3,
        tp_spread=0.00280879347504896,

        # 新增参数
        risk_probility=0.004008499977011849,
        risk_probility_buy=0.031118271581968452,
        risk_probility_sell=0.023784354643037974,
        enter_lot=5000,
        grid_spread=0.00767954703433127,
        sl_ratio=3.3508665898525365,

        # 止盈参数 - 简化版本
        take_profit_levels=[
            (0.00280879347504896, 1.0)  # 使用tp_spread值，全平
        ]
    )

def get_performance_summary():
    """获取优化结果的业绩摘要"""
    return {
        'optimization_timestamp': '20250710_005823',
        'best_objective_value': -0.195355,
        'train_performance': {
            'total_return': -0.0029,
            'sharpe_ratio': 0.0386,
            'max_drawdown': 0.0149,
            'win_rate': 0.3088,
            'profit_factor': 0.6551,
            'total_trades': 557
        },
        'test_performance': {
            'total_return': -0.0060,
            'sharpe_ratio': -0.0951,
            'max_drawdown': 0.0120,
            'win_rate': 0.3243,
            'profit_factor': 0.7669,
            'total_trades': 111
        },
        'generalization_ratio': 2.0754
    }
