#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的功能
"""

def test_import():
    """测试导入"""
    try:
        from qmt_optuna_backtest import optimize_parameters_with_validation, OptimizationConfig
        print('✅ 导入成功')
        return True
    except Exception as e:
        print(f'❌ 导入失败: {e}')
        return False

def test_config():
    """测试配置创建"""
    try:
        from qmt_optuna_backtest import OptimizationConfig
        config = OptimizationConfig(n_trials=1)
        print('✅ 配置创建成功')
        return True
    except Exception as e:
        print(f'❌ 配置创建失败: {e}')
        return False

if __name__ == "__main__":
    print("🔧 测试修复...")
    
    success = True
    success &= test_import()
    success &= test_config()
    
    if success:
        print("\n✅ 修复成功！")
        print("现在可以运行: python run_optuna_optimization.py")
    else:
        print("\n❌ 仍有问题")
