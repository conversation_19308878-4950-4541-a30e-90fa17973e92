"""
Optuna优化后的最佳参数配置 - 自动生成
生成时间: 2025-07-10 09:31:09
优化目标值: -0.169466
训练集收益率: -0.0033
测试集收益率: -0.0064
泛化性能: 1.97
"""

from qmt_optuna_backtest import BacktestConfig

def get_optimized_config() -> BacktestConfig:
    """获取自动优化的最佳参数配置"""
    return BacktestConfig(
        # 基础参数
        initial_cash=1000000.0,
        max_position=226814,
        tick_size=0.001,
        commission_rate=0.0001,

        # 策略核心参数 (自动优化)
        dt=1728,
        window_size=262311,
        order_update_interval=18526,

        # 网格交易参数
        grid_levels=2,
        base_quantity=1190,
        max_spread=0.0012209505884183802,
        min_spread=0.0014968342099424274,

        # 原策略参数
        ema_span=0.009351488790423142,
        ema_spread=-0.002735728398080674,
        intensity_nspread=8,
        tp_spread=0.002737609209654056,

        # 新增参数
        risk_probility=0.04232482009198159,
        risk_probility_buy=0.005133215717144151,
        risk_probility_sell=0.03325365716027002,
        enter_lot=5000,
        grid_spread=0.004156277797983889,
        sl_ratio=5.666307465465463,

        # 止盈参数 - 简化版本
        take_profit_levels=[
            (0.002737609209654056, 1.0)  # 使用tp_spread值，全平
        ]
    )

def get_performance_summary():
    """获取优化结果的业绩摘要"""
    return {
        'optimization_timestamp': '20250710_093103',
        'best_objective_value': -0.169466,
        'train_performance': {
            'total_return': -0.0033,
            'sharpe_ratio': 0.0352,
            'max_drawdown': 0.0192,
            'win_rate': 0.3422,
            'profit_factor': 0.6823,
            'total_trades': 567
        },
        'test_performance': {
            'total_return': -0.0064,
            'sharpe_ratio': -0.0845,
            'max_drawdown': 0.0125,
            'win_rate': 0.3427,
            'profit_factor': 0.7069,
            'total_trades': 143
        },
        'generalization_ratio': 1.9710
    }
