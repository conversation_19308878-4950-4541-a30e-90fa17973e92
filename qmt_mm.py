#encoding:gbk
'''

'''
#import pandas as pd
import numpy as np
# import talib
import math
from typing import List, Tuple
import datetime
import time
# from scipy import linregress

class EmpiricalIntensityEstimator:
	"""
	限价单信息的容器
	"""
	class LimitOrderTracker:
		def __init__(self, start_ts: int, order_price: float):
			self.start_ts = start_ts
			self.order_price = order_price

	class Fill:
		def is_order_filled(self, filled_price: float, order_price: float) -> bool:
			raise NotImplementedError()

	class SellFill(Fill):
		def is_order_filled(self, filled_price: float, order_price: float) -> bool:
			return filled_price > order_price

	class BuyFill(Fill):
		def is_order_filled(self, filled_price: float, order_price: float) -> bool:
			return filled_price < order_price

	def __init__(self, spread: float, spread_direction: float, dt: int):
		"""
		@param spread 与中间价的距离，买入限价单使用负号，卖出限价单使用正号
		@param spread_direction -1 表示卖出限价单，1 表示买入限
		"""
		self.spread = spread
		self.dt = dt
		self.initializing = True
		self.last_price = float('nan')
		self.last_limit_order_inserted = 0
		self.live_trackers: List[LimitOrderTracker] = [] # type: ignore
		self.live_trackers_start_time_sum = 0
		self.finished_trackers: List[Tuple[int, int]] = []
		self.finished_trackers_wait_time_sum = 0
		self.fill_comp = self.SellFill() if spread_direction > 0 else self.BuyFill()

	def on_tick(self, ref_price: float, fill_price: float, ts: int, window_start: int):

		if self.initializing:
			self.initializing = False
			self.last_limit_order_inserted = ts - self.dt

		while self.last_limit_order_inserted + self.dt < ts:
			self.last_limit_order_inserted += self.dt
			self.live_trackers.append(
				self.LimitOrderTracker(self.last_limit_order_inserted, self.last_price + self.spread)
			)
			self.live_trackers_start_time_sum += self.last_limit_order_inserted

		if self.last_limit_order_inserted + self.dt == ts:
			self.last_limit_order_inserted = ts
			self.live_trackers.append(self.LimitOrderTracker(ts, ref_price + self.spread))
			self.live_trackers_start_time_sum += ts

		self.last_price = ref_price

		for tracker in self.live_trackers[:]:
			if window_start > tracker.start_ts:
				self.live_trackers.remove(tracker)
				self.live_trackers_start_time_sum -= tracker.start_ts
				continue

			if self.fill_comp.is_order_filled(fill_price, tracker.order_price):
				self.live_trackers.remove(tracker)
				self.live_trackers_start_time_sum -= tracker.start_ts
				duration = ts - tracker.start_ts
				self.finished_trackers.append((tracker.start_ts, duration))
				self.finished_trackers_wait_time_sum += duration

	def estimate_intensity(self, ts: int, window_start: int) -> float:
		
		for tracker in self.finished_trackers[:]:
			if tracker[0] < window_start:
				self.finished_trackers.remove(tracker)
				self.finished_trackers_wait_time_sum -= tracker[1]

		if self.live_trackers and ts != self.live_trackers[-1].start_ts:
			for tracker in self.live_trackers[:]:
				if window_start > tracker.start_ts:
					self.live_trackers.remove(tracker)
					self.live_trackers_start_time_sum -= tracker.start_ts

		if not self.live_trackers:
			return 0.0

		numerator = self.dt * len(self.finished_trackers)  
		denominator = (  
			len(self.live_trackers) * ts  
			- self.live_trackers_start_time_sum  
			+ self.finished_trackers_wait_time_sum  
		)  

		if denominator == 0:  
			return 0.0 
		return max(float(numerator) / denominator, 1e-6)
	
class AbstractAkSolver:
	def __init__(self, spread_specification: np.ndarray):

		self.spread_specification = np.abs(spread_specification)

	def solve_ak(self, intensities: np.ndarray) -> np.ndarray:

		raise NotImplementedError("子类必须实现此方法")

class AkMultiCurveSolver(AbstractAkSolver):
	def __init__(self, spread_specification: np.ndarray):
		"""
		近似求解A k
		"""
		super().__init__(spread_specification)
		n_estimates = len(spread_specification) * (len(spread_specification) - 1) // 2
		self.k_estimates = np.zeros(n_estimates)
		self.a_estimates = np.zeros(n_estimates)

	def solve_ak(self, intensities: np.ndarray) -> np.ndarray:

		est_idx = 0
		with np.errstate(divide='ignore', invalid='ignore'):
			for i in range(len(intensities) - 1):
				for j in range(i + 1, len(intensities)):
					
					self.k_estimates[est_idx] = (
						np.log(intensities[j] / intensities[i]) / 
						(self.spread_specification[i] - self.spread_specification[j])
					)
					self.a_estimates[est_idx] = (
						intensities[i] * np.exp(self.k_estimates[est_idx] * self.spread_specification[i])
					)
					est_idx += 1
		
		return np.array([np.mean(self.a_estimates), np.mean(self.k_estimates)])

class SpreadIntensityCurve: 
	def __init__(self, spread_step: float, n_spreads: int, dt: int):
		# 这里 dt = 3000ms 了 由于 qmt本身的限制
		self.intensity_estimators: List[EmpiricalIntensityEstimator] = []
		spread_specification = np.zeros(n_spreads)
		self.intensity_estimates = np.zeros(n_spreads)
		
		for i in range(n_spreads):
			spread_specification[i] = i * spread_step
			self.intensity_estimators.append(
				EmpiricalIntensityEstimator(spread_specification[i], np.sign(spread_step), dt)
			)
		self.solver = AkMultiCurveSolver(spread_specification)

	def on_tick(self, ref_price: float, fill_price: float, ts: int, window_start: int):

		for estimator in self.intensity_estimators:
			estimator.on_tick(ref_price, fill_price, ts, window_start)

	def estimate_ak(self, ts: int, window_start: int) -> np.ndarray:
		for i, estimator in enumerate(self.intensity_estimators):
			self.intensity_estimates[i] = estimator.estimate_intensity(ts, window_start)
		spread_specification = np.array([estimator.spread for estimator in self.intensity_estimators])
		return self.solver.solve_ak(self.intensity_estimates)


class ASMarket_Maker:
	def __init__(self):
		self.etf_list = []  # 可T+0的etf
		self.account = ''  # 交易账号
		self.account_type = ''  # 账号类型
		self.waiting_list = []  # 未查到委托列表
		self.etf_list = '513120.SH' # 513280
		self.ema_span = 0.005  
		self.ema_spread = -0.004
		self.intensity_nspread = 5
		self.intensity_window = 180 # 估算时间长度 单位秒
		self.sell_enble = False
		self.timestamp_update_price = 0
		self.spread_specification = None
		self.fee = 1/10000
		self.order_update_interval = 15000 
		# self.solver = 'linregress'
		self.buy_est = None
		self.sell_est = None
		self.price_step = 0.001 #最小价格差 ETF=0.001 stock = 0.01
		self.trade_etf = '513120.SH'
		self.buy_price = 0
		self.sell_price = 0
		self.exit_price = 0
		self.last_bid_spread = 0.004  # 上次有效的bid spread
		self.last_ask_spread = 0.004  # 上次有效的ask spread
		self.tp_spread = 0.004 # 止盈 spread 如果 exit pct = 0.5 第二次止盈是成本价的 2*tp_spread 
		self.reach_tp1 = False # 止盈 加仓后是否有第一次止盈
		self.if_add_vol = False
		self.last_valid_time = 0
		self.enter_lot = 5000 #
		self.grid_spread = 0.006
		self.grid_spreadnet_param = [1, 1.5, 2, 2.5]
		self.grid_spreadqty_param = [1, 1, 2, 2]
		self.grid_add_layer = 0
		self.enter_price = 0
		self.exit_pct = 0.5
		self.risk_probility = 0.03
		self.timestamp_update_price = 0
		self.pending_sell_orders = {}  # 待成交的卖出订单 {order_type: timestamp}
		self.pending_buy_orders = {}   # 待成交的买入订单 {order_type: timestamp}  
		self.order_timeout = 5000
		self.internal_cost = -1.0
		self.sl_ratio = 4

# 强度价差预测
def get_intensity(target_spread: float, a: float, k: float) -> float:
	return a * math.exp(-k * target_spread)

def get_spread(target_intensity: float, a: float, k: float) -> float:
	return -(math.log(target_intensity / a)) / k

def dynamic_mid(ask, bid, ask_qty = 0.001, bid_qty = 0.001):
	imbalance = max(ask_qty, bid_qty) / min(ask_qty, bid_qty)
	if imbalance > 3.0:
		return (ask * bid_qty + bid * ask_qty) / (bid_qty + ask_qty)
	else:
		return (ask + bid) / 2

def check_and_clean_pending_orders(current_time):
	"""
	检查并清理超时的待处理订单
	"""
	# 清理超时的卖出订单
	for order_type in list(MM.pending_sell_orders.keys()):
		if current_time - MM.pending_sell_orders[order_type] > MM.order_timeout:
			print(f"清理超时的卖出订单: {order_type}")
			del MM.pending_sell_orders[order_type]
	
	# 清理超时的买入订单
	for order_type in list(MM.pending_buy_orders.keys()):
		if current_time - MM.pending_buy_orders[order_type] > MM.order_timeout:
			print(f"清理超时的买入订单: {order_type}")
			del MM.pending_buy_orders[order_type]

def is_order_pending(order_type, order_direction='sell'):
	"""
	检查指定类型的订单是否仍在待处理状态
	"""
	if order_direction == 'sell':
		return order_type in MM.pending_sell_orders
	else:
		return order_type in MM.pending_buy_orders

def record_pending_order(order_type, current_time, order_direction='sell'):
	"""
	记录待处理的订单
	"""
	if order_direction == 'sell':
		MM.pending_sell_orders[order_type] = current_time
		print(f"记录待处理卖出订单: {order_type}")
	else:
		MM.pending_buy_orders[order_type] = current_time
		print(f"记录待处理买入订单: {order_type}")

def clear_pending_order_by_reason(reason):
	"""
	根据交易原因清理对应的待处理订单
	"""
	# 映射交易原因到订单类型
	reason_to_order_type = {
		'take_profit_1': 'take_profit_1',
		'take_profit_2_full': 'take_profit_2_full', 
		'simple_profit': 'simple_profit',
		'stop_loss':'stop_loss'
	}
	
	order_type = reason_to_order_type.get(reason)
	if order_type and order_type in MM.pending_sell_orders:
		del MM.pending_sell_orders[order_type]
		print(f"清理已成交的订单: {order_type}")

def order_callback(order_info):
	"""
	订单回调函数，用于处理订单状态变化
	在QMT中，当订单状态发生变化时会调用此函数
	"""
	try:
		# 检查订单是否已成交
		if hasattr(order_info, 'm_nOrderStatus') and order_info.m_nOrderStatus == 48:  # 48表示全部成交
			# 根据订单信息推断交易类型并清理对应的待处理订单
			order_remark = getattr(order_info, 'm_strOrderRemark', '')
			
			# 这里可以根据订单备注或其他字段来判断订单类型
			# 由于QMT的限制，我们可能需要根据实际的订单字段来调整
			if 'sell' in order_remark.lower():
				# 简化处理：如果是卖出订单，清理所有待处理的卖出订单
				# 在实际使用中，应该根据更具体的信息来匹配订单类型
				MM.pending_sell_orders.clear()
				print("检测到卖出订单成交，清理所有待处理卖出订单")
			elif 'buy' in order_remark.lower():
				# 如果是买入订单，清理所有待处理的买入订单
				MM.pending_buy_orders.clear()
				print("检测到买入订单成交，清理所有待处理买入订单")
		
	except Exception as e:
		print(f"处理订单回调时出错: {e}")
	
MM = ASMarket_Maker()

def max_vol_spread(tick):
	max_value = max(tick['bidVol'])
	max_index = tick['bidVol'].index(max_value)
	return (max_index + 1) * 0.001
	
def init(ContextInfo):
	MM.etf_list = [
	'513120.SH', '159776.SZ', '159615.SZ', '513200.SH', '513060.SH', '159892.SZ', '513280.SH', '159718.SZ',
	'513360.SH', '513700.SH', '513020.SH', '501021.SH', '513980.SH', '513860.SH', '513090.SH', '501311.SH',
	'513530.SH', '513320.SH', '159750.SZ', '513150.SH', '159788.SZ', '159747.SZ', '159960.SZ', '159954.SZ',
	'513680.SH', '159792.SZ', '159850.SZ', '159712.SZ', '159823.SZ', '160416.SH', '513770.SH', '513160.SH',
	'513010.SH', '510900.SH', '161128.SH', '159726.SZ', '159822.SZ', '501025.SH', '513590.SH', '159751.SZ',
	'159607.SZ', '513890.SH', '513990.SH', '513080.SH', '164705.SH', '159866.SZ', '513260.SH', '164824.SH',
	'513580.SH', '160322.SH', '518860.SH', '513690.SH', '511380.SH', '511260.SH', '513300.SH', '511060.SH',
	'511180.SH', '511970.SH', '511020.SH', '511270.SH', '511950.SH', '511220.SH', '511910.SH', '159832.SZ',
	'511600.SH', '511620.SH', '511010.SH', '159816.SZ', '511930.SH', '511670.SH', '159972.SZ', '511880.SH',
	'511920.SH', '511660.SH', '511820.SH', '511800.SH', '511830.SH', '511700.SH', '511900.SH', '511850.SH',
	'511990.SH', '511690.SH', '511360.SH', '511650.SH', '161116.SH', '160717.SH', '159741.SZ', '159740.SZ',
	'159711.SZ', '159005.SZ', '513900.SH', '513660.SH', '513380.SH', '513180.SH', '501302.SH', '159001.SZ']
	MM.account = '********'
	MM.account_type = 'STOCK'
	print(f'ETF T+0做市初始化中，可用标的有 {len(MM.etf_list)}只ETF')
	MM.buy_est = SpreadIntensityCurve(spread_step=-MM.price_step, n_spreads= MM.intensity_nspread, dt = 1500)
	MM.sell_est = SpreadIntensityCurve(spread_step=MM.price_step, n_spreads= MM.intensity_nspread, dt = 1500)
	return MM
def handlebar(ContextInfo):
	# === 新增：订单状态管理 ===
	now = datetime.datetime.now()
	high_vol = True
	current_time = get_market_time("SH")
	unix_time = now.timestamp() * 1000
	if not((current_time > 93000 and current_time < 101500) or(current_time > 143000 and current_time < 150000)):
		high_vol = False
		MM.tp_spread = 0.002
		MM.intensity_window = 240
		MM.order_update_interval = 13
		MM.risk_probility = 0.015
	else:
		MM.tp_spread = 0.004
		MM.intensity_window = 180
		MM.order_update_interval = 6
		MM.risk_probility = 0.01

	
	# 清理超时的订单
	check_and_clean_pending_orders(unix_time)
	etf_holdings = []
	position_info = get_trade_detail_data(MM.account, 'stock','position')

	for position in position_info:
		stock_code = position.m_strInstrumentID
		# 检查持仓代码是否在ETF列表中（带交易所后缀）
		if stock_code == MM.trade_etf[:6]:
			# 记录持仓详细信息
			holding_data = {
				'code': stock_code,
				'volume': position.m_nVolume,      # 持仓数量
				# 'available': position.m_nCanUseVolume,  # 可用数量
				'cost': position.m_dOpenPrice          # 成本价
			}
			etf_holdings.append(holding_data)
	if etf_holdings:
		if etf_holdings[0]['volume'] > 0:
			MM.sell_enble = True
			print('当前持仓: ' ,etf_holdings[0])
			print('内部维护持仓价格: ',MM.internal_cost)
			if abs(etf_holdings[0]['cost'] - MM.internal_cost) > 0.0015 and MM.internal_cost > 0:
				print('持仓价格不一致！！建议检查')
				etf_holdings[0]['cost'] = MM.internal_cost
		else:
			print('当前木有持仓')
	else:
		print('当前木有持仓')
		MM.sell_enble = False
	tick = ContextInfo.get_full_tick([MM.trade_etf])
	last_price = tick[MM.trade_etf]['lastPrice']
	tick = tick[MM.trade_etf]
	mid_price = dynamic_mid(tick['askPrice'][0], tick['bidPrice'][0], tick['askVol'][0], tick['bidVol'][0])
	now = datetime.datetime.now()
	unix_time = now.timestamp() * 1000 # 用这个时间戳去计算AS相关的东西
	MM.sell_est.on_tick(mid_price, tick['askPrice'][0], unix_time, unix_time - MM.intensity_window * 1000)
	MM.buy_est.on_tick(mid_price, tick['bidPrice'][0], unix_time, unix_time - MM.intensity_window * 1000)

	buy_ak = MM.buy_est.estimate_ak(unix_time, unix_time - MM.intensity_window * 1000)
	sell_ak = MM.sell_est.estimate_ak(unix_time, unix_time - MM.intensity_window * 1000)
	#print(buy_ak,sell_ak)
	# if not np.any(np.isnan(buy_ak)):  

	bid_spread = 0.000
	ask_spread = 0.000
	if not np.any(np.isnan(buy_ak)):  
		if  buy_ak[0] * buy_ak[1] != 0:
			filtered_bid_intensity = MM.buy_est.intensity_estimates[MM.buy_est.intensity_estimates > 1e-6]
			bid_spread = get_spread(MM.risk_probility, buy_ak[0], buy_ak[1])
			bid_spread = max(bid_spread, 0.0015)
			print(buy_ak,sell_ak,' ',filtered_bid_intensity)
		if  sell_ak[0] * sell_ak[1] != 0:
			filtered_ask_intensity = MM.sell_est.intensity_estimates[MM.sell_est.intensity_estimates > 1e-6]
			if len(filtered_ask_intensity) and filtered_ask_intensity[-1] * sell_ak[0] > 0:
				if len(filtered_ask_intensity) < 4:
					ask_spread = get_spread(filtered_ask_intensity[-1], sell_ak[0], sell_ak[1]) + 0.001
				else:
					ask_spread = get_spread(filtered_ask_intensity[3], sell_ak[0], sell_ak[1])
	spread_lifetime = 9000
	maxvol_bid_spread = max_vol_spread(tick)
	if unix_time - MM.last_valid_time > spread_lifetime and bid_spread <= 0:    
		bid_spread = 0.004 if high_vol else 0.003
		ask_spread = 0.004 if high_vol else 0.003 
		if bid_spread > maxvol_bid_spread:
			bid_spread = maxvol_bid_spread * 0.6667 + bid_spread * 0.3333
	elif unix_time - MM.last_valid_time <= spread_lifetime and bid_spread <= 0:
		bid_spread = MM.last_bid_spread
		ask_spread = MM.last_ask_spread
	else:
		MM.last_valid_time = unix_time
		if bid_spread > 0.005:
			bid_spread = maxvol_bid_spread * 0.8 + bid_spread * 0.2
		elif bid_spread > maxvol_bid_spread:
			bid_spread = max(maxvol_bid_spread * 0.618 + bid_spread * 0.382, 0.0013)
			
	MM.last_bid_spread = bid_spread
	MM.last_ask_spread = ask_spread
	if unix_time - MM.timestamp_update_price > MM.order_update_interval * 1000: # 暂定 15s 计算一次价格
		MM.buy_price = round((mid_price - MM.last_bid_spread)*1000)/1000
		MM.sell_price = math.floor((last_price + MM.last_ask_spread)*1000)/1000
		MM.timestamp_update_price = unix_time
		print('价格更新：')
		print(f'bid挂单价格: {MM.buy_price:.3f}  bid spread', bid_spread, 'max bidvol spread', maxvol_bid_spread)
		# print(f'ask挂单价格: {MM.sell_price:.3f}')
	# 开始 交易部分
	# sl first
	if etf_holdings:
		if ((etf_holdings[0]['cost'] - last_price) * etf_holdings[0]['volume']) / (MM.tp_spread * MM.enter_lot) > MM.sl_ratio:
			if is_order_pending('stop_loss', 'sell'):
					print("止损订单已发送，等待成交...")
					return
			record_pending_order('stop_loss', unix_time, 'sell')
			passorder(24, 
					  1101, 
					  MM.account, 
					  MM.trade_etf,
					  6, 
					  -1, 
					  etf_holdings[0]['volume'], 
					  'ETF T+0', 
					  1, 
					  'sell', 
					  ContextInfo)
			MM.if_add_vol = False
			print("止损 操作: 卖出标的 ",etf_holdings[0]['volume'],"份 建议暂停策略 避免连续亏损")
			MM.sell_enble = False
			MM.grid_add_layer = 0
			MM.internal_cost = -1.0
			MM.enter_price = 0
			return
			
	
	if not MM.sell_enble:
		if tick['askPrice'][0]<= MM.buy_price:
			# === 新增：检查是否已有待处理的开仓买入订单 ===
			if is_order_pending('initial_buy', 'buy'):
				print("开仓买入订单已发送，等待成交...")
				return
			
			# 记录待处理订单
			record_pending_order('initial_buy', unix_time, 'buy')
			
			passorder(23, 
					  1101, 
					  MM.account, 
					  MM.trade_etf,
					  14, 
					  -1, 
					  MM.enter_lot, 
					  'ETF T+0', 
					  1, 
					  'buy', 
					  ContextInfo)
			print("操作: 买入标的 ",MM.enter_lot,"手",' 当前仓位均价: ',MM.internal_cost)
			MM.internal_cost = tick['askPrice'][0]
			MM.enter_price = tick['askPrice'][0]
			return
	else: #考虑加仓 or 出场离场 目前简单版就 1-2次出场 有些标的还需要考虑盘口的接盘侠够不够 现在的港股医药暂时不考虑
		MM.exit_price = etf_holdings[0]['cost'] + MM.tp_spread - 0.0001# MM.exit_price = sell price
		print('获利价格：', MM.exit_price,'  加仓价格：',etf_holdings[0]['cost'] - MM.grid_spread)
		if MM.if_add_vol:
			
			reenter_price = MM.enter_price
			reenter_lot = MM.enter_lot
			for i in range(MM.grid_add_layer + 1):
				reenter_price -= MM.grid_spread * MM.grid_spreadnet_param[i]
				reenter_lot = MM.enter_lot * MM.grid_spreadqty_param[i]
			if tick['askPrice'][0] <= reenter_price:
				# === 新增：检查是否已有待处理的加仓买入订单 ===
				if is_order_pending('grid_buy_1', 'buy'):
					print("加仓买入订单已发送，等待成交...")
					return
				
				# 记录待处理订单
				record_pending_order('grid_buy_1', unix_time, 'buy')
				
				passorder(23, 
						  1101, 
						  MM.account, 
						  MM.trade_etf,
						  5, 
						  -1, 
						  reenter_lot, 
						  'ETF T+0', 
						  1, 
						  'buy add', 
						  ContextInfo)
				MM.reach_tp1 = False
				if MM.internal_cost>0:
					MM.internal_cost = (etf_holdings[0]['volume'] * MM.internal_cost + reenter_lot * reenter_price)/(etf_holdings[0]['volume'] + reenter_lot)
				MM.grid_add_layer += 1
				print("操作: 加仓标的 ",MM.enter_lot,"手",' 当前仓位均价: ',MM.internal_cost)
				return
			elif tick['bidPrice'][0]>= MM.exit_price and not MM.reach_tp1:
				# === 新增：检查是否已有待处理的第一次止盈订单 ===
				if is_order_pending('take_profit_1', 'sell'):
					print("第一次止盈订单已发送，等待成交...")
					return
				
				tp1_quantity = (etf_holdings[0]['volume']+100) // 200 * 100
				
				# 记录待处理订单
				record_pending_order('take_profit_1', unix_time, 'sell')
				
				passorder(24, 
						  1101, 
						  MM.account, 
						  MM.trade_etf,
						  6, 
						  -1, 
						  tp1_quantity, 
						  'ETF T+0', 
						  1, 
						  'sell', 
						  ContextInfo)
				MM.reach_tp1 = True
				print("到达tp1 操作: 卖出标的 ",tp1_quantity ,"份")
				MM.grid_add_layer //= 2
				return
			elif tick['bidPrice'][0] >= MM.exit_price + MM.tp_spread  and MM.reach_tp1:
				# === 新增：检查是否已有待处理的第二次止盈订单 ===
				if is_order_pending('take_profit_2_full', 'sell'):
					print("第二次止盈订单已发送，等待成交...")
					return
				
				# 记录待处理订单
				record_pending_order('take_profit_2_full', unix_time, 'sell')
				
				passorder(24, 
						  1101, 
						  MM.account, 
						  MM.trade_etf,
						  6, 
						  -1, 
						  etf_holdings[0]['volume'], 
						  'ETF T+0', 
						  1, 
						  'sell', 
						  ContextInfo)
				MM.reach_tp1 = False #到达 TP2
				MM.if_add_vol = False
				MM.internal_cost = -1.0
				print("到达tp2 操作: 卖出标的 ",etf_holdings[0]['volume'],"份")
				MM.grid_add_layer = 0
				MM.enter_price = 0
				return
		else:
			if tick['bidPrice'][0]>= MM.exit_price:
				# === 新增：检查是否已有待处理的简单止盈订单 ===
				if is_order_pending('simple_profit', 'sell'):
					print("简单止盈订单已发送，等待成交...")
					return
				
				# 记录待处理订单
				record_pending_order('simple_profit', unix_time, 'sell')
				
				passorder(24, 
						  1101, 
						  MM.account, 
						  MM.trade_etf,
						  6, 
						  -1, 
						  etf_holdings[0]['volume'], 
						  'ETF T+0', 
						  1, 
						  'sell', 
						  ContextInfo)
				MM.if_add_vol = False
				print("获利 操作: 卖出标的 ",etf_holdings[0]['volume'],"份")
				MM.sell_enble = False
				MM.grid_add_layer = 0
				MM.internal_cost = -1.0
				MM.enter_price = 0
				return
			elif tick['askPrice'][0] < etf_holdings[0]['cost'] - MM.grid_spread:
				# === 新增：检查是否已有待处理的加仓买入订单 ===
				if is_order_pending('grid_buy_2', 'buy'):
					print("加仓买入订单已发送，等待成交...")
					return
				
				# 记录待处理订单
				record_pending_order('grid_buy_2', unix_time, 'buy')
				
				reenter_price = MM.enter_price
				reenter_lot = MM.enter_lot
				for i in range(MM.grid_add_layer + 1):
					reenter_price -= MM.grid_spread * MM.grid_spreadnet_param[i]
					reenter_lot = MM.enter_lot * MM.grid_spreadqty_param[i]
				passorder(23, 
						  1101, 
						  MM.account, 
						  MM.trade_etf,
						  14, 
						  -1, 
						  reenter_lot, 
						  'ETF T+0', 
						  1, 
						  'buy add', 
						  ContextInfo)
				MM.if_add_vol = True
				if MM.internal_cost>0:
					MM.internal_cost = (etf_holdings[0]['volume'] * MM.internal_cost + reenter_lot * reenter_price)/(etf_holdings[0]['volume'] + reenter_lot)
				MM.grid_add_layer += 1
				print("操作: 加仓标的 ",MM.enter_lot,"份",' 当前仓位均价: ',MM.internal_cost)
				return
