#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试绘图功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

def create_minimal_test_data():
    """创建最小测试数据"""
    base_time = datetime.now()
    timestamps = [base_time + timedelta(minutes=i) for i in range(100)]
    
    data = pd.DataFrame({
        'timestamp': [int(t.timestamp() * 1000) for t in timestamps],
        'last_price': 100 + np.cumsum(np.random.normal(0, 0.1, 100)),
        'bid_price': 100 + np.cumsum(np.random.normal(0, 0.1, 100)) - 0.01,
        'ask_price': 100 + np.cumsum(np.random.normal(0, 0.1, 100)) + 0.01
    })
    
    return data

def test_plot_function():
    """测试绘图函数"""
    try:
        print("🔧 开始调试绘图功能...")
        
        # 导入必要的模块
        from qmt_optuna_backtest import plot_trading_analysis_simple, QMTOptunaBacktest, BacktestConfig, Trade
        print("✅ 模块导入成功")
        
        # 创建测试数据
        test_data = create_minimal_test_data()
        print(f"✅ 测试数据创建成功: {len(test_data)} 个点")
        
        # 创建测试回测实例
        config = BacktestConfig(initial_cash=1000000.0)
        backtest = QMTOptunaBacktest(config)
        
        # 添加简单的交易记录
        backtest.trades = [
            Trade(timestamp=test_data['timestamp'].iloc[20], side='buy', price=100.5, quantity=1000, pnl=0, reason='test'),
            Trade(timestamp=test_data['timestamp'].iloc[80], side='sell', price=101.0, quantity=1000, pnl=500, reason='test')
        ]
        
        # 添加简单的权益曲线
        backtest.equity_curve = [
            (test_data['timestamp'].iloc[0], 1000000),
            (test_data['timestamp'].iloc[50], 1000250),
            (test_data['timestamp'].iloc[99], 1000500)
        ]
        
        print("✅ 回测数据创建成功")
        
        # 确保保存目录存在
        save_dir = "test_debug_plots"
        os.makedirs(save_dir, exist_ok=True)
        print(f"✅ 保存目录创建: {save_dir}")
        
        # 测试绘图
        print("📊 开始绘图测试...")
        plot_files = plot_trading_analysis_simple(
            backtest, 
            test_data, 
            save_dir=save_dir, 
            filename_prefix="debug_test"
        )
        
        print(f"📈 绘图完成，返回文件: {plot_files}")
        
        # 检查文件是否真的存在
        if plot_files:
            for plot_type, file_path in plot_files.items():
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    print(f"✅ 文件存在: {file_path} ({file_size} bytes)")
                else:
                    print(f"❌ 文件不存在: {file_path}")
        else:
            print("❌ 没有返回任何文件")
        
        # 检查目录内容
        if os.path.exists(save_dir):
            files = os.listdir(save_dir)
            print(f"📂 目录内容: {files}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_matplotlib():
    """检查matplotlib是否正常工作"""
    try:
        import matplotlib
        print(f"✅ matplotlib版本: {matplotlib.__version__}")
        
        import matplotlib.pyplot as plt
        print("✅ pyplot导入成功")
        
        # 设置后端
        backend = matplotlib.get_backend()
        print(f"📊 当前后端: {backend}")
        
        # 尝试创建简单图表
        fig, ax = plt.subplots(1, 1, figsize=(6, 4))
        ax.plot([1, 2, 3], [1, 4, 2])
        ax.set_title('测试图表')
        
        test_file = "test_matplotlib.png"
        plt.savefig(test_file, dpi=100)
        plt.close()
        
        if os.path.exists(test_file):
            print(f"✅ matplotlib绘图测试成功: {test_file}")
            os.remove(test_file)  # 清理测试文件
            return True
        else:
            print("❌ matplotlib绘图测试失败")
            return False
            
    except Exception as e:
        print(f"❌ matplotlib检查失败: {e}")
        return False

if __name__ == "__main__":
    print("🔍 绘图功能调试...")
    print("=" * 50)
    
    # 检查matplotlib
    matplotlib_ok = check_matplotlib()
    
    if matplotlib_ok:
        print("\n" + "=" * 50)
        # 测试绘图函数
        plot_ok = test_plot_function()
        
        if plot_ok:
            print("\n✅ 绘图功能调试成功！")
            print("现在应该可以正常生成图表了")
        else:
            print("\n❌ 绘图功能有问题")
    else:
        print("\n❌ matplotlib有问题，需要先解决")
    
    print("\n💡 如果还是没有图表，请检查:")
    print("1. 运行时是否有绘图相关的错误信息")
    print("2. optimization_results目录中是否有PNG文件")
    print("3. 是否有权限问题")
