"""
测试科学验证的最优配置
使用训练/测试集分离优化得到的参数在所有数据上进行回测
"""

import os
from optimized_config import get_optimized_config
from qmt_optuna_backtest import QMTOptunaBacktest, get_data_files, load_multiple_files

def test_validated_config():
    """测试科学验证的配置"""
    print("=== 测试科学验证的最优配置 ===")
    
    # 获取配置
    config = get_optimized_config()
    
    print(f"配置参数:")
    print(f"  挂单更新频率: {config.order_update_interval/1000:.1f}秒")
    print(f"  决策间隔: {config.dt/1000:.1f}秒")
    print(f"  评估窗口: {config.window_size/60000:.1f}分钟")
    print(f"  最大持仓: {config.max_position}")
    print(f"  基础下单量: {config.base_quantity}")
    print(f"  网格层数: {config.grid_levels}")
    print(f"  价差范围: {config.min_spread:.3f}% - {config.max_spread:.3f}%")
    
    # 获取所有数据文件
    data_dir = "backtest_data/merged"
    all_files = get_data_files(data_dir)
    
    print(f"\n=== 在所有数据上测试 ===")
    print(f"数据文件: {len(all_files)} 个")
    
    # 加载所有数据
    all_data = load_multiple_files(all_files)
    
    # 运行回测
    backtest = QMTOptunaBacktest(config)
    results = backtest.run_backtest(all_data)
    
    print(f"\n=== 完整回测结果 ===")
    print(f"总收益率: {results['total_return']:.4f} ({results['total_return']:.2%})")
    print(f"夏普比率: {results['sharpe_ratio']:.4f}")
    print(f"最大回撤: {results['max_drawdown']:.4f} ({results['max_drawdown']:.2%})")
    print(f"总交易次数: {results['total_trades']}")
    print(f"胜率: {results['win_rate']:.4f} ({results['win_rate']:.1%})")
    print(f"盈亏比: {results['profit_factor']:.4f}")
    print(f"最终资金: {results['final_equity']:.2f}")
    print(f"已实现盈亏: {results['realized_pnl']:.2f}")
    print(f"未实现盈亏: {results['unrealized_pnl']:.2f}")
    
    # 分析结果
    print(f"\n=== 结果分析 ===")
    if results['total_return'] > 0:
        print("✓ 策略整体盈利")
    else:
        print("✗ 策略整体亏损")
        
    if results['sharpe_ratio'] > 1.0:
        print("✓ 夏普比率优秀 (>1.0)")
    elif results['sharpe_ratio'] > 0.5:
        print("⚠ 夏普比率良好 (0.5-1.0)")
    else:
        print("✗ 夏普比率较低 (<0.5)")
        
    if results['max_drawdown'] < 0.05:
        print("✓ 回撤控制优秀 (<5%)")
    elif results['max_drawdown'] < 0.10:
        print("⚠ 回撤控制良好 (5%-10%)")
    else:
        print("✗ 回撤较大 (>10%)")
    
    # 计算年化收益率 (假设13个交易日约为1个月)
    trading_days = len(all_files)
    annualized_return = results['total_return'] * (252 / trading_days)
    print(f"\n=== 年化估算 ===")
    print(f"交易天数: {trading_days}")
    print(f"年化收益率: {annualized_return:.2%}")
    print(f"年化夏普比率: {results['sharpe_ratio'] * (252/trading_days)**0.5:.2f}")
    
    return results

if __name__ == "__main__":
    test_validated_config() 