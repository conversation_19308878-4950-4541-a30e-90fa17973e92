import pytest
from utils.AS_api.src import IntensityInfo
from utils.AS_api.src.calibration import AkMultiCurveSolver, AkRegressionSolver, AbstractAkSolver

"""
测试生成的 spread - intensity 数据上的具体 A 和 k 求解器
"""

class TestAkSolvers:
    """
    测试 A 和 k 求解器
    """
    
    a = 10
    k = 1.5
    eps = 1e-10  # 机器精度

    @pytest.mark.parametrize("solver,intensities", [
        (AkMultiCurveSolver([1, 2, 3, 4, 5]), []),
        (AkRegressionSolver([1, 2, 3, 4, 5]), [])
    ])
    def test_ak_solver(self, solver: AbstractAkSolver, intensities: list[float]):
        """测试 A 和 k 求解器"""
        sln = solver.solve_ak(intensities)
        # 断言与配置的 A 和 k 相等
        assert abs(self.a - sln[0]) < self.eps, "A 错误"
        assert abs(self.k - sln[1]) < self.eps, "k 错误"

    @staticmethod
    def solver_provider():
        """提供测试数据"""
        spread = [1, 2, 3, 4, 5]  # 测试 spread
        intensities = [IntensityInfo.get_intensity(s, TestAkSolvers.a, TestAkSolvers.k) 
                      for s in spread]
        
        return [
            (AkMultiCurveSolver(spread), intensities),
            (AkRegressionSolver(spread), intensities)
        ]
