import pytest
import os
from typing import List
from tqdm import tqdm  # 进度条替换为tqdm
from utils.AS_api.src.calibration import AkSolverFactory, SpreadIntensityCurve
from utils.AS_api.src import IntensityInfo

class TestIntensityEstimator:
    """
    测试 IntensityEstimator 类
    """
    
    def setup_method(self):
        """测试配置"""
        self.spread_step = 0.00001
        self.n_steps = 5
        self.w = 1000 * 60 * 10  # 滑动窗口30分钟
        self.dt = 1000 * 15  # 时间缩放15秒
        self.test_spread = 3 * self.spread_step
        self.test_data = []

    def prepare_test(self):
        """准备测试数据（兼容路径）"""
        test_data_path = os.path.join("src", "test", "resources", "tick.csv")
        with open(test_data_path) as f:
            self.test_data = [
                TickData(float(parts[0]), float(parts[1]), int(parts[2]))
                for parts in (line.strip().split(",") for line in f.readlines()[1:])
            ]

    @pytest.mark.parametrize("test_case", ["default"])  # 参数化测试调整
    def test_buy_estimator(self, test_case):
        """测试买入强度估计器"""
        self.prepare_test()
        
        sf = AkSolverFactory(AkSolverFactory.SolverType.MULTI_CURVE)
        buy_execution_intensity = SpreadIntensityCurve(-self.spread_step, self.n_steps, self.dt, sf)
        buy_empirical_intensities = buy_execution_intensity.intensity_estimates
        
        calibration_ts = self.test_data[0].ts + self.w
        
        # 使用tqdm替换ProgressBar
        for td in tqdm(self.test_data, desc="buy test"):
            window_start = td.ts - self.w
            buy_execution_intensity.on_tick((td.b + td.a) / 2, td.a, td.ts, window_start)
            
            if calibration_ts <= td.ts:
                estimates = buy_execution_intensity.estimate_ak(td.ts, window_start)
                for i in range(1, len(buy_empirical_intensities)):
                    assert buy_empirical_intensities[i-1] > buy_empirical_intensities[i], \
                        f"Buy λ估计异常 {i-1}/{i} == {buy_empirical_intensities[i-1]:.6f}/{buy_empirical_intensities[i]:.6f}"

class TickData:
    """测试数据容器"""
    def __init__(self, b: float, a: float, ts: int):
        self.b = b
        self.a = a
        self.ts = ts

class EstimationOutputWriter:
    """CSV输出辅助类（优化路径处理）"""
    def __init__(self, file_name: str, n_spreads: int):
        output_dir = os.path.join("target", "intensity-log")
        os.makedirs(output_dir, exist_ok=True)
        
        self.file_path = os.path.join(output_dir, file_name)
        self._init_writer(n_spreads)
        
    def _init_writer(self, n_spreads):
        """初始化CSV写入器"""
        with open(self.file_path, "w") as f:
            header = [
                "bid,ask,mid price,ts,buy λ(test δ),sell λ(test δ),",
                *[f"buy λ({i}δ)," for i in range(n_spreads)],
                *[f"sell λ({i}δ)," for i in range(n_spreads)],
                "buy δ(test λ),sell δ(test λ), buy A, buy k, sell A, sell k\n"
            ]
            f.write("".join(header))
            
    def write_line(self, bid: float, ask: float, ts: int, intensity_info: IntensityInfo,
                  buy_empirical: List[float], sell_empirical: List[float]):
        """写入数据行"""
        line = [
            f"{bid:.5f},{ask:.5f},{(bid + ask)/2:.5f},{ts},",
            f"{intensity_info.get_buy_fill_intensity(self.test_spread):.5f},",
            f"{intensity_info.get_sell_fill_intensity(self.test_spread):.5f},",
            *[f"{i:.5f}," for i in buy_empirical],
            *[f"{i:.5f}," for i in sell_empirical],
            f"{intensity_info.buy_spread:.5f},{intensity_info.sell_spread:.5f},",
            f"{intensity_info.buy_a:.5f},{intensity_info.buy_k:.5f},",
            f"{intensity_info.sell_a:.5f},{intensity_info.sell_k:.5f}\n"
        ]
        with open(self.file_path, "a") as f:
            f.write("".join(line))
# import pytest
# import os
# from typing import List, Tuple
# from concurrent.futures import ThreadPoolExecutor
# from me.tongfei.progressbar import ProgressBar
# from utils.AS_api.src.calibration import AkSolverFactory, SpreadIntensityCurve
# from utils.AS_api.src import IntensityEstimator, IntensityInfo

# """
# 在 "src/test/resources/tick.csv" 中的历史数据上测试估计器
# 详细的测试输出保存到 target/intensity-log/ 文件夹
# """

# class TestIntensityEstimator:
#     """
#     测试 IntensityEstimator 类
#     """
    
#     def setup_method(self):
#         """测试配置"""
#         self.spread_step = 0.00001
#         self.n_steps = 5
#         self.w = 1000 * 60 * 10  # 滑动窗口 30 分钟
#         self.dt = 1000 * 15  # 时间缩放 15 秒
        
#         # 测试 spread
#         self.test_spread = 3 * self.spread_step
        
#         self.test_data = []  # 保存预加载的测试数据
        
#     def prepare_test(self):
#         """准备测试数据"""
#         # 加载测试数据
#         with open("src/test/resources/tick.csv") as f:
#             self.test_data = [
#                 TickData(float(parts[0]), float(parts[1]), int(parts[2]))
#                 for parts in (line.strip().split(",") for line in f.readlines()[1:])
#             ]
            
#     @pytest.mark.parametrize("test_data", prepare_test.__func__())
#     def test_buy_estimator(self):
#         """测试买入强度估计器"""
#         sf = AkSolverFactory(AkSolverFactory.SolverType.MULTI_CURVE)
#         buy_execution_intensity = SpreadIntensityCurve(-self.spread_step, self.n_steps, self.dt, sf)
#         buy_empirical_intensities = buy_execution_intensity.intensity_estimates
        
#         calibration_ts = self.test_data[0].ts + self.w
#         for td in ProgressBar(self.test_data, "buy test: "):
#             window_start = td.ts - self.w
#             buy_execution_intensity.on_tick((td.b + td.a) / 2, td.a, td.ts, window_start)
            
#             if calibration_ts <= td.ts:
#                 estimates = buy_execution_intensity.estimate_ak(td.ts, window_start)
#                 i_prev = 0
#                 for i in range(1, len(buy_empirical_intensities)):
#                     assert buy_empirical_intensities[i_prev] > buy_empirical_intensities[i], \
#                         f"Buy λ estimate {i_prev}/{i} == {buy_empirical_intensities[i_prev]:.6f}/{buy_empirical_intensities[i]:.6f}"
#                     i_prev = i
                    
#     # 其他测试方法类似转换...
    
# class TickData:
#     """测试数据容器"""
    
#     def __init__(self, b: float, a: float, ts: int):
#         self.b = b
#         self.a = a
#         self.ts = ts
        
# class EstimationOutputWriter:
#     """用于格式化并写入包含详细估计信息的输出 .csv 文件的辅助类"""
    
#     def __init__(self, file_name: str, n_spreads: int):
#         output_dir = "target/intensity-log/"
#         os.makedirs(output_dir, exist_ok=True)
        
#         self.writer = open(os.path.join(output_dir, file_name), "w")
#         self.sb = []
#         # 写入表头
#         self.sb.append("bid,ask,mid price,ts,buy λ(test δ),sell λ(test δ),")
#         self.sb.extend(f"buy λ({i}δ)," for i in range(n_spreads))
#         self.sb.extend(f"sell λ({i}δ)," for i in range(n_spreads))
#         self.sb.append("buy δ(test λ),sell δ(test λ), buy A, buy k, sell A, sell k\n")
#         self.writer.write("".join(self.sb))
        
#     def write_line(self, bid: float, ask: float, ts: int, intensity_info: IntensityInfo,
#                   buy_empirical_intensities: List[float], sell_empirical_intensities: List[float]):
#         """写入一行数据"""
#         buy_intensity = intensity_info.get_buy_fill_intensity(self.test_spread)
#         sell_intensity = intensity_info.get_sell_fill_intensity(self.test_spread)
#         buy_spread = intensity_info.get_buy_spread(buy_intensity)
#         sell_spread = intensity_info.get_sell_spread(sell_intensity)
        
#         # 重置字符串构建器
#         self.sb = []
        
#         self.sb.append(f"{bid:.5f},")
#         self.sb.append(f"{ask:.5f},")
#         self.sb.append(f"{(bid + ask) / 2:.5f},")
#         self.sb.append(f"{ts},")
#         self.sb.append(f"{buy_intensity:.5f},")
#         self.sb.append(f"{sell_intensity:.5f},")
        
#         self.sb.extend(f"{intensity:.5f}," for intensity in buy_empirical_intensities)
#         self.sb.extend(f"{intensity:.5f}," for intensity in sell_empirical_intensities)
        
#         self.sb.append(f"{buy_spread:.5f},")
#         self.sb.append(f"{sell_spread:.5f},")
#         self.sb.append(f"{intensity_info.buy_a:.5f},")
#         self.sb.append(f"{intensity_info.buy_k:.5f},")
#         self.sb.append(f"{intensity_info.sell_a:.5f},")
#         self.sb.append(f"{intensity_info.sell_k:.5f}\n")
        
#         self.writer.write("".join(self.sb))
        
#     def close(self):
#         """关闭写入器"""
#         self.writer.close()
