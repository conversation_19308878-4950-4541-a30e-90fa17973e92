#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试trade数据不进行5档填充
"""

import pandas as pd
import numpy as np
from datetime import datetime

def test_trade_no_5level_filling():
    """测试trade数据不会被添加5档列"""
    try:
        print("🧪 测试trade数据不进行5档填充...")
        
        # 模拟_load_snapshot函数的处理
        print("\n📊 模拟snapshot数据处理:")
        snapshot_data = {
            '代码': ['513120'] * 3,
            '时间': ['09:30:00', '09:30:03', '09:30:06'],
            '成交价': [100.0, 100.1, 100.2],
            '申买价1': [99.99, 100.09, 100.19],
            '申卖价1': [100.01, 100.11, 100.21],
            '申买量1': [5000, 4800, 5200],
            '申卖量1': [4500, 4700, 4300],
            '申买价2': [99.98, 100.08, 100.18],
            '申卖价2': [100.02, 100.12, 100.22],
            '申买量2': [3000, 2800, 3200],
            '申卖量2': [2500, 2700, 2300],
        }
        
        snapshot_df = pd.DataFrame(snapshot_data)
        snapshot_df['timestamp'] = [1000, 3000, 6000]
        
        # 应用列映射（模拟_load_snapshot的逻辑）
        col_map = {
            "成交价": "last_price",
            "申买价1": "bid_price_1",
            "申卖价1": "ask_price_1", 
            "申买量1": "bid_vol_1",
            "申卖量1": "ask_vol_1",
            "申买价2": "bid_price_2",
            "申卖价2": "ask_price_2",
            "申买量2": "bid_vol_2", 
            "申卖量2": "ask_vol_2",
        }
        
        for src, dst in col_map.items():
            if src in snapshot_df.columns:
                snapshot_df[dst] = pd.to_numeric(snapshot_df[src], errors="coerce")
        
        # 添加向后兼容列
        snapshot_df["bid_price"] = snapshot_df["bid_price_1"]
        snapshot_df["ask_price"] = snapshot_df["ask_price_1"]
        
        print(f"  Snapshot列数: {len(snapshot_df.columns)}")
        print(f"  Snapshot包含的5档列: {[col for col in snapshot_df.columns if any(col.startswith(prefix) for prefix in ['bid_price_', 'ask_price_', 'bid_vol_', 'ask_vol_'])]}")
        
        # 模拟_load_trade函数的处理
        print("\n📊 模拟trade数据处理:")
        trade_data = {
            '代码': ['513120'] * 2,
            '时间': ['09:30:01', '09:30:04'],
            '成交价': [100.05, 100.15],
            '成交量': [500, 800],
            '买卖方向': ['B', 'S'],
        }
        
        trade_df = pd.DataFrame(trade_data)
        trade_df['timestamp'] = [1500, 4500]
        trade_df['trade_price'] = trade_df['成交价']
        trade_df['trade_qty'] = trade_df['成交量']
        trade_df['trade_side'] = trade_df['买卖方向']
        
        print(f"  Trade列数: {len(trade_df.columns)}")
        print(f"  Trade列名: {list(trade_df.columns)}")
        print(f"  Trade是否包含5档列: {any(col.startswith(prefix) for col in trade_df.columns for prefix in ['bid_price_', 'ask_price_', 'bid_vol_', 'ask_vol_'])}")
        
        # 模拟合并前的列统一处理
        print("\n🔄 模拟合并前处理:")
        
        # 基础列（两个数据框都需要）
        basic_cols = [
            "last_price", "bid_price", "ask_price", "bid_vol", "ask_vol",
            "last_volume", "trade_price", "trade_qty", "trade_side",
        ]
        
        # 5档列（只有snapshot需要）
        level_cols = []
        for i in range(1, 3):  # 只测试前2档
            level_cols.extend([
                f"bid_price_{i}", f"ask_price_{i}", 
                f"bid_vol_{i}", f"ask_vol_{i}"
            ])
        
        # 确保基础列在两个数据框中都存在
        for col in basic_cols:
            if col not in snapshot_df:
                snapshot_df[col] = np.nan
            if col not in trade_df:
                trade_df[col] = np.nan
        
        # 只为snapshot数据确保5档列存在（修正后的逻辑）
        for col in level_cols:
            if col not in snapshot_df:
                snapshot_df[col] = np.nan
            # trade数据不添加5档列！
        
        print(f"  处理后Snapshot列数: {len(snapshot_df.columns)}")
        print(f"  处理后Trade列数: {len(trade_df.columns)}")
        print(f"  Trade仍然不包含5档列: {not any(col.startswith(prefix) for col in trade_df.columns for prefix in ['bid_price_', 'ask_price_', 'bid_vol_', 'ask_vol_'])}")
        
        # 模拟合并
        print("\n🔗 模拟合并过程:")
        try:
            # 这里应该会因为列不匹配而需要特殊处理
            # 我们需要确保合并时正确处理列差异
            
            # 获取所有唯一列
            all_cols = set(snapshot_df.columns) | set(trade_df.columns)
            
            # 为每个数据框添加缺失的列
            for col in all_cols:
                if col not in snapshot_df:
                    snapshot_df[col] = np.nan
                if col not in trade_df:
                    trade_df[col] = np.nan
            
            merged = pd.concat([snapshot_df, trade_df], ignore_index=True, sort=False)
            merged = merged.sort_values("timestamp").reset_index(drop=True)
            
            print(f"  合并成功，共 {len(merged)} 行")
            print(f"  合并后列数: {len(merged.columns)}")
            
            # 检查trade行的5档数据
            trade_rows = merged[merged['trade_price'].notna()]
            print(f"  Trade行数: {len(trade_rows)}")
            
            if len(trade_rows) > 0:
                sample_trade = trade_rows.iloc[0]
                print(f"  Trade行的bid_price_1: {sample_trade.get('bid_price_1', 'N/A')}")
                print(f"  Trade行的ask_price_1: {sample_trade.get('ask_price_1', 'N/A')}")
                
                # 检查是否为NaN（应该是NaN，因为trade数据本身没有5档信息）
                if pd.isna(sample_trade.get('bid_price_1')):
                    print("✅ Trade行的5档数据正确为NaN（未填充）")
                else:
                    print("❌ Trade行的5档数据被错误填充了")
                    return False
            
            return True
            
        except Exception as e:
            print(f"❌ 合并失败: {e}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 测试trade数据不进行5档填充...")
    print("=" * 60)
    
    if test_trade_no_5level_filling():
        print("\n✅ 测试通过！")
        print("\n🎉 修正验证:")
        print("- Trade数据不会被添加不必要的5档列")
        print("- 只有snapshot数据包含5档信息")
        print("- 合并过程正确处理列差异")
        print("- Trade行的5档数据保持为NaN（直到前向填充）")
        
        print("\n📋 正确的处理流程:")
        print("1. Snapshot数据: 保留完整5档信息")
        print("2. Trade数据: 只保留成交信息，不添加5档列")
        print("3. 合并时: 统一列结构（trade的5档列设为NaN）")
        print("4. 前向填充: 将snapshot的5档信息填充到后续trade行")
        
        print("\n🚀 现在可以正确合并数据:")
        print("python merge_snapshot_trade.py sh513120_20250613 --data-dir backtest_data")
        
    else:
        print("\n❌ 测试失败，需要进一步修正")
    
    print("\n💡 优化要点:")
    print("- Trade数据本身不包含5档信息，不应该被强制添加")
    print("- 5档信息通过前向填充从snapshot传递到trade")
    print("- 减少不必要的内存使用和处理时间")
