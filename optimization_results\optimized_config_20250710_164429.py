"""
Optuna优化后的最佳参数配置 - 自动生成
生成时间: 2025-07-10 16:45:07
优化目标值: 2.466202
训练集收益率: 0.3169
测试集收益率: 0.4873
泛化性能: 1.54
"""

from qmt_optuna_backtest import BacktestConfig

def get_optimized_config() -> BacktestConfig:
    """获取自动优化的最佳参数配置"""
    return BacktestConfig(
        # 基础参数
        initial_cash=1000000.0,
        max_position=216149,
        tick_size=0.001,
        commission_rate=0.0001,

        # 策略核心参数 (自动优化)
        dt=1171,
        window_size=277608,
        order_update_interval=28786,

        # 网格交易参数
        grid_levels=3,
        base_quantity=1265,
        max_spread=0.003439610978351007,
        min_spread=0.0013344422152258603,

        # 原策略参数
        ema_span=0.009822934341104216,
        ema_spread=-0.003425752815430519,
        intensity_nspread=7,
        tp_spread=0.005430262475833158,

        # 新增参数
        risk_probility=0.005796071243906947,
        risk_probility_buy=0.05,
        risk_probility_sell=0.1,
        enter_lot=5000,
        grid_spread=0.007225927812742462,
        sl_ratio=4.307575219431642,

        # 止盈参数 - 简化版本
        take_profit_levels=[
            (0.005430262475833158, 1.0)  # 使用tp_spread值，全平
        ]
    )

def get_performance_summary():
    """获取优化结果的业绩摘要"""
    return {
        'optimization_timestamp': '20250710_164429',
        'best_objective_value': 2.466202,
        'train_performance': {
            'total_return': 0.3169,
            'sharpe_ratio': 7.4924,
            'max_drawdown': 0.1285,
            'win_rate': 0.7198,
            'profit_factor': inf,
            'total_trades': 389
        },
        'test_performance': {
            'total_return': 0.4873,
            'sharpe_ratio': 15.7709,
            'max_drawdown': 0.1068,
            'win_rate': 0.5166,
            'profit_factor': 298.7037,
            'total_trades': 391
        },
        'generalization_ratio': 1.5377
    }
