#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动继续最新优化的脚本
"""

import os
import sys
from qmt_optuna_backtest import (
    optimize_parameters_with_validation, 
    OptimizationConfig,
    list_existing_studies,
    get_latest_study,
    load_latest_study
)

def show_existing_studies():
    """显示现有的studies"""
    studies = list_existing_studies()
    
    if not studies:
        print("📭 没有找到现有的优化研究")
        return False
    
    print(f"📚 发现 {len(studies)} 个现有的优化研究:")
    print("=" * 60)
    
    for i, study_name in enumerate(studies[:5], 1):  # 显示最近5个
        print(f"  {i}. {study_name}")
        
        # 尝试获取基本信息
        try:
            study = load_existing_study(study_name)
            print(f"     试验数: {len(study.trials)}, 最佳值: {study.best_value:.6f}")
        except Exception as e:
            print(f"     状态: 无法加载 ({e})")
        print()
    
    if len(studies) > 5:
        print(f"  ... 还有 {len(studies)-5} 个研究")
    
    return True

def auto_continue_optimization():
    """自动继续最新的优化"""
    try:
        # 显示现有studies
        if not show_existing_studies():
            print("🆕 将创建新的优化研究")
            auto_continue = False
        else:
            latest_study = get_latest_study()
            print(f"🔄 将自动继续最新的研究: {latest_study}")
            auto_continue = True
        
        # 配置优化参数
        opt_config = OptimizationConfig(
            initial_cash=1000000.0,
            commission_rate_range=(0.0001, 0.0001),
            max_position_range=(50000, 250000),
            enable_latency=False,
            dt_range=(1000, 3000),
            window_size_range=(180000, 300000),
            order_update_interval_range=(9000, 30000),
            enter_lot_range=(5000, 5000),
            grid_spread_range=(0.004, 0.012),
            risk_probility_range=(0.0001, 0.05),
            risk_probility_buy_range=(0.0001, 0.05),
            risk_probility_sell_range=(0.0001, 0.07),
            tp_spread_range=(0.001, 0.006),
            n_trials=10,  # 继续优化10次
            n_jobs=1,
            timeout=None,
            sl_ratio_range=(2, 8)
        )
        
        # 数据目录
        data_dir = "backtest_data/merged"
        
        if not os.path.exists(data_dir):
            print(f"❌ 数据目录不存在: {data_dir}")
            return
        
        print(f"\n🚀 开始优化...")
        print(f"数据目录: {data_dir}")
        print(f"试验次数: {opt_config.n_trials}")
        print(f"自动继续: {'是' if auto_continue else '否'}")
        
        # 运行优化
        results = optimize_parameters_with_validation(
            data_dir=data_dir,
            opt_config=opt_config,
            train_ratio=0.7,
            auto_continue=auto_continue
        )
        
        print(f"\n✅ 优化完成!")
        print(f"最佳值: {results['best_value']:.6f}")
        print(f"总试验数: {results['n_trials_completed']}")
        
        if 'continued_from' in results:
            print(f"继续自: {results['continued_from']}")
            print(f"新增试验: {results['additional_trials']}")
        
        if 'save_directory' in results:
            print(f"结果保存在: {results['save_directory']}")
        
    except Exception as e:
        print(f"❌ 优化过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🔄 自动继续优化工具")
    print("=" * 40)
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "list":
            show_existing_studies()
        elif command == "auto":
            auto_continue_optimization()
        else:
            print(f"❌ 未知命令: {command}")
            print("使用方法:")
            print("  python auto_continue_optimization.py list  # 列出现有studies")
            print("  python auto_continue_optimization.py auto  # 自动继续优化")
    else:
        # 默认行为：自动继续优化
        auto_continue_optimization()

if __name__ == "__main__":
    main()
