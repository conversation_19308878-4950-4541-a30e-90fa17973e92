"""
Optuna优化后的最佳参数配置 - 自动生成
生成时间: 2025-07-10 00:41:19
优化目标值: -0.220744
训练集收益率: -0.0033
测试集收益率: -0.0060
泛化性能: 1.83
"""

from qmt_optuna_backtest import BacktestConfig

def get_optimized_config() -> BacktestConfig:
    """获取自动优化的最佳参数配置"""
    return BacktestConfig(
        # 基础参数
        initial_cash=1000000.0,
        max_position=240243,
        tick_size=0.001,
        commission_rate=0.0001,

        # 策略核心参数 (自动优化)
        dt=2730,
        window_size=242001,
        order_update_interval=18783,

        # 网格交易参数
        grid_levels=2,
        base_quantity=1179,
        max_spread=0.0020650544112115242,
        min_spread=0.001366813308328615,

        # 原策略参数
        ema_span=0.005708916509371265,
        ema_spread=-0.0038280945392234374,
        intensity_nspread=6,
        tp_spread=0.0020511625539216725,

        # 新增参数
        risk_probility=0.0418885749607598,
        risk_probility_buy=0.01372709342136636,
        risk_probility_sell=0.024997607588584617,
        enter_lot=5000,
        grid_spread=0.0082548168153579,
        sl_ratio=5.623404887138501,

        # 止盈参数 - 简化版本
        take_profit_levels=[
            (0.0020511625539216725, 1.0)  # 使用tp_spread值，全平
        ]
    )

def get_performance_summary():
    """获取优化结果的业绩摘要"""
    return {
        'optimization_timestamp': '20250710_004110',
        'best_objective_value': -0.220744,
        'train_performance': {
            'total_return': -0.0033,
            'sharpe_ratio': 0.0364,
            'max_drawdown': 0.0149,
            'win_rate': 0.3501,
            'profit_factor': 0.5848,
            'total_trades': 597
        },
        'test_performance': {
            'total_return': -0.0060,
            'sharpe_ratio': -0.0644,
            'max_drawdown': 0.0120,
            'win_rate': 0.3742,
            'profit_factor': 0.8068,
            'total_trades': 155
        },
        'generalization_ratio': 1.8252
    }
