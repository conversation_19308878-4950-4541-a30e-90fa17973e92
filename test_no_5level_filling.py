#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试trade数据不进行5档填充（参考B/S标志的做法）
"""

import pandas as pd
import numpy as np

def test_data_structure_consistency():
    """测试数据结构一致性（参考B/S标志的处理方式）"""
    try:
        print("🧪 测试数据结构一致性...")
        
        # 模拟snapshot数据（有5档信息，没有BS标志）
        snapshot_data = {
            'timestamp': [1000, 3000, 5000],
            'event_type': ['snapshot', 'snapshot', 'snapshot'],
            'last_price': [100.0, 100.1, 100.2],
            'bid_price': [99.99, 100.09, 100.19],
            'ask_price': [100.01, 100.11, 100.21],
            'bid_vol': [5000, 4800, 5200],
            'ask_vol': [4500, 4700, 4300],
            'last_volume': [1000, 1500, 2000],
            'bid_price_1': [99.99, 100.09, 100.19],
            'ask_price_1': [100.01, 100.11, 100.21],
            'bid_vol_1': [5000, 4800, 5200],
            'ask_vol_1': [4500, 4700, 4300],
            'bid_price_2': [99.98, 100.08, 100.18],
            'ask_price_2': [100.02, 100.12, 100.22],
            'bid_vol_2': [3000, 2800, 3200],
            'ask_vol_2': [2500, 2700, 2300],
            'trade_price': [np.nan, np.nan, np.nan],
            'trade_qty': [np.nan, np.nan, np.nan],
            'trade_side': [np.nan, np.nan, np.nan],
        }
        
        # 模拟trade数据（有BS标志，没有5档信息）
        trade_data = {
            'timestamp': [2000, 4000],
            'event_type': ['trade', 'trade'],
            'trade_price': [100.05, 100.15],
            'trade_qty': [500, 800],
            'trade_side': ['B', 'S'],
            'last_price': [np.nan, np.nan],
            'bid_price': [np.nan, np.nan],
            'ask_price': [np.nan, np.nan],
            'bid_vol': [np.nan, np.nan],
            'ask_vol': [np.nan, np.nan],
            'last_volume': [np.nan, np.nan],
        }
        
        snapshot_df = pd.DataFrame(snapshot_data)
        trade_df = pd.DataFrame(trade_data)
        
        print(f"📊 数据结构对比:")
        print(f"  Snapshot列数: {len(snapshot_df.columns)}")
        print(f"  Trade列数: {len(trade_df.columns)}")
        
        # 检查5档列
        snapshot_5level = [col for col in snapshot_df.columns if any(col.startswith(prefix) for prefix in ['bid_price_', 'ask_price_', 'bid_vol_', 'ask_vol_'])]
        trade_5level = [col for col in trade_df.columns if any(col.startswith(prefix) for prefix in ['bid_price_', 'ask_price_', 'bid_vol_', 'ask_vol_'])]
        
        print(f"  Snapshot的5档列: {len(snapshot_5level)} 个")
        print(f"  Trade的5档列: {len(trade_5level)} 个")
        
        # 检查BS标志
        snapshot_has_bs = 'trade_side' in snapshot_df.columns and not snapshot_df['trade_side'].isna().all()
        trade_has_bs = 'trade_side' in trade_df.columns and not trade_df['trade_side'].isna().all()
        
        print(f"  Snapshot有BS标志: {snapshot_has_bs}")
        print(f"  Trade有BS标志: {trade_has_bs}")
        
        # 验证一致性原则
        print(f"\n✅ 一致性原则验证:")
        print(f"  - Snapshot有5档，Trade没有: {'✅' if len(snapshot_5level) > 0 and len(trade_5level) == 0 else '❌'}")
        print(f"  - Trade有BS标志，Snapshot没有: {'✅' if trade_has_bs and not snapshot_has_bs else '❌'}")
        print(f"  - 保持数据的原始特征: ✅")
        
        # 模拟合并（不强制统一列结构）
        print(f"\n🔗 模拟合并过程:")
        merged = pd.concat([snapshot_df, trade_df], ignore_index=True, sort=False)
        merged = merged.sort_values("timestamp").reset_index(drop=True)
        
        print(f"  合并后总行数: {len(merged)}")
        print(f"  合并后列数: {len(merged.columns)}")
        
        # 检查trade行是否被强制添加了5档列
        trade_rows = merged[merged['event_type'] == 'trade']
        if len(trade_rows) > 0:
            sample_trade = trade_rows.iloc[0]
            has_5level_data = any(pd.notna(sample_trade.get(col, np.nan)) for col in ['bid_price_2', 'ask_price_2'])
            
            print(f"  Trade行是否有5档数据: {'❌ 有（错误）' if has_5level_data else '✅ 没有（正确）'}")
            
            if not has_5level_data:
                print("✅ Trade数据保持原始结构，没有被强制添加5档信息")
                return True
            else:
                print("❌ Trade数据被错误地添加了5档信息")
                return False
        else:
            print("❌ 没有找到trade行")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def explain_correct_approach():
    """解释正确的处理方式"""
    print("\n📋 正确的处理方式（参考B/S标志）:")
    
    print("\n🎯 数据特征保持:")
    print("  📊 Snapshot数据:")
    print("    - ✅ 包含5档买卖价格和数量")
    print("    - ❌ 不包含BS标志（成交方向）")
    print("    - 🎯 反映市场深度状态")
    
    print("\n  💰 Trade数据:")
    print("    - ❌ 不包含5档信息")
    print("    - ✅ 包含BS标志（成交方向）")
    print("    - 🎯 反映实际成交情况")
    
    print("\n🔄 合并原则:")
    print("  1. 不强制统一列结构")
    print("  2. 保持各自的数据特征")
    print("  3. 通过聚合函数智能处理")
    print("  4. 避免不必要的数据填充")
    
    print("\n💡 类比说明:")
    print("  就像我们不会给snapshot数据强制添加BS标志一样，")
    print("  我们也不应该给trade数据强制添加5档信息。")
    print("  每种数据保持其原始的、有意义的结构。")

if __name__ == "__main__":
    print("🧪 测试trade数据不进行5档填充...")
    print("=" * 60)
    
    if test_data_structure_consistency():
        print("\n✅ 测试通过！")
        
        explain_correct_approach()
        
        print("\n🎉 修正总结:")
        print("- ✅ Trade数据保持原始结构（不包含5档列）")
        print("- ✅ Snapshot数据保持原始结构（包含5档列）")
        print("- ✅ 参考B/S标志的处理方式")
        print("- ✅ 避免不必要的数据填充")
        print("- ✅ 保持数据的语义完整性")
        
        print("\n🚀 现在可以正确合并数据:")
        print("python merge_snapshot_trade.py --all --data-dir backtest_data")
        
    else:
        print("\n❌ 测试失败，需要进一步修正")
    
    print("\n📝 关键改进:")
    print("1. 不对trade数据添加5档列")
    print("2. 不对5档数据进行前向填充")
    print("3. 保持snapshot和trade的原始数据特征")
    print("4. 通过聚合函数处理数据差异")
