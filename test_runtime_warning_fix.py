#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试RuntimeWarning修复
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta

def test_position_calculation():
    """测试持仓计算是否会产生RuntimeWarning"""
    try:
        from qmt_optuna_backtest import QMTOptunaBacktest, BacktestConfig
        
        # 创建配置
        config = BacktestConfig(
            initial_cash=1000000.0,
            commission_rate=0.0001,
            max_position=10000,
            dt=1500,
            window_size=300000,
            order_update_interval=15000,
            enter_lot=1000,
            grid_spread=0.008,
            risk_probility=0.03,
            risk_probility_buy=0.01,
            risk_probility_sell=0.015,
            tp_spread=0.004,
            sl_ratio=4.0,
            min_spread=0.001
        )
        
        # 创建回测实例
        backtest = QMTOptunaBacktest(config)
        print("✅ 回测实例创建成功")
        
        # 测试初始状态
        print(f"\n🔍 初始状态:")
        print(f"  position.quantity: {backtest.position.quantity}")
        print(f"  position.avg_price: {backtest.position.avg_price}")
        print(f"  cash: {backtest.cash}")
        
        # 测试第一次买入（从0持仓开始）
        print(f"\n🧪 测试第一次买入:")
        timestamp = int(datetime.now().timestamp() * 1000)
        price = 100.0
        quantity = 1000
        
        # 执行买入交易
        backtest.execute_trade('buy', price, quantity, timestamp)
        
        print(f"  买入后 quantity: {backtest.position.quantity}")
        print(f"  买入后 avg_price: {backtest.position.avg_price}")
        print(f"  买入后 cash: {backtest.cash}")
        
        # 检查是否有NaN或无穷大
        if np.isnan(backtest.position.avg_price) or not np.isfinite(backtest.position.avg_price):
            print("❌ 平均价格计算出现NaN或无穷大")
            return False
        
        # 测试第二次买入（增加持仓）
        print(f"\n🧪 测试第二次买入:")
        price2 = 101.0
        quantity2 = 500
        
        backtest.execute_trade('buy', price2, quantity2, timestamp + 1000)
        
        print(f"  第二次买入后 quantity: {backtest.position.quantity}")
        print(f"  第二次买入后 avg_price: {backtest.position.avg_price}")
        
        # 验证平均价格计算
        expected_avg = (100.0 * 1000 + 101.0 * 500) / (1000 + 500)
        print(f"  期望平均价格: {expected_avg:.6f}")
        print(f"  实际平均价格: {backtest.position.avg_price:.6f}")
        
        if abs(backtest.position.avg_price - expected_avg) < 0.000001:
            print("✅ 平均价格计算正确")
        else:
            print("❌ 平均价格计算错误")
            return False
        
        # 测试卖出
        print(f"\n🧪 测试卖出:")
        price3 = 102.0
        quantity3 = 800
        
        backtest.execute_trade('sell', price3, quantity3, timestamp + 2000)
        
        print(f"  卖出后 quantity: {backtest.position.quantity}")
        print(f"  卖出后 avg_price: {backtest.position.avg_price}")
        print(f"  卖出后 realized_pnl: {backtest.position.realized_pnl}")
        
        # 检查交易记录
        print(f"\n📊 交易记录:")
        for i, trade in enumerate(backtest.trades):
            print(f"  交易{i+1}: {trade.side} {trade.quantity}@{trade.price:.2f}, pnl={trade.pnl:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """测试边界情况"""
    try:
        from qmt_optuna_backtest import QMTOptunaBacktest, BacktestConfig
        
        config = BacktestConfig(
            initial_cash=1000000.0,
            commission_rate=0.0001,
            max_position=10000
        )
        
        backtest = QMTOptunaBacktest(config)
        timestamp = int(datetime.now().timestamp() * 1000)
        
        print(f"\n🧪 测试边界情况:")
        
        # 测试零数量交易
        print("  测试零数量交易...")
        backtest.execute_trade('buy', 100.0, 0, timestamp)
        print(f"    quantity: {backtest.position.quantity}, avg_price: {backtest.position.avg_price}")
        
        # 测试极小价格
        print("  测试极小价格...")
        backtest.execute_trade('buy', 0.001, 1000, timestamp + 1000)
        print(f"    quantity: {backtest.position.quantity}, avg_price: {backtest.position.avg_price}")
        
        # 测试极大价格
        print("  测试极大价格...")
        backtest.execute_trade('buy', 999999.0, 1, timestamp + 2000)
        print(f"    quantity: {backtest.position.quantity}, avg_price: {backtest.position.avg_price}")
        
        # 检查是否有异常值
        if (np.isnan(backtest.position.avg_price) or 
            not np.isfinite(backtest.position.avg_price) or
            backtest.position.avg_price < 0):
            print("❌ 边界情况测试失败")
            return False
        
        print("✅ 边界情况测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 边界情况测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 测试RuntimeWarning修复...")
    print("=" * 50)
    
    # 测试持仓计算
    position_ok = test_position_calculation()
    
    if position_ok:
        # 测试边界情况
        edge_ok = test_edge_cases()
        
        if edge_ok:
            print("\n✅ 所有测试通过！")
            print("\n🎉 RuntimeWarning修复成功:")
            print("- 修复了平均价格计算中的除零错误")
            print("- 添加了数值安全检查")
            print("- 处理了NaN和无穷大值")
            print("- 正确处理零持仓情况")
            
            print("\n🚀 现在可以安全运行优化:")
            print("python run_optuna_optimization.py")
            
        else:
            print("\n❌ 边界情况测试失败")
    else:
        print("\n❌ 持仓计算测试失败")
    
    print("\n📋 主要修复:")
    print("1. 在买入时检查old_quantity是否为0")
    print("2. 安全计算平均价格，避免除零")
    print("3. 在卖出时检查avg_price是否有效")
    print("4. 添加NaN和无穷大值检查")
